import sqlite3
from datetime import datetime, timedelta
import random

def create_sample_messages():
    """
    إنشاء رسائل تجريبية لاختبار نظام المراسلة
    """
    print("بدء إنشاء رسائل تجريبية...")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # الحصول على المستخدمين الموجودين
        cursor.execute("SELECT id, name, username FROM user")
        users = cursor.fetchall()
        
        if len(users) < 2:
            print("يجب وجود مستخدمين على الأقل لإنشاء رسائل تجريبية")
            return
        
        # رسائل تجريبية
        sample_messages = [
            {
                'subject': 'ترحيب بالنظام الجديد',
                'content': 'مرحباً بكم في نظام المراسلة الجديد!\n\nيمكنكم الآن التواصل بسهولة مع جميع أعضاء الفريق من خلال هذا النظام.\n\nالميزات الجديدة:\n- إرسال رسائل فردية أو جماعية\n- إرفاق ملفات\n- تتبع حالة القراءة\n- الرد على الرسائل\n\nنتمنى لكم تجربة ممتعة!',
                'priority': 'high',
                'message_type': 'notification'
            },
            {
                'subject': 'تحديث سياسات العمل',
                'content': 'تم تحديث سياسات العمل الخاصة بالشركة.\n\nيرجى مراجعة الوثائق المرفقة والالتزام بالسياسات الجديدة.\n\nفي حالة وجود أي استفسارات، يرجى التواصل مع إدارة الموارد البشرية.',
                'priority': 'normal',
                'message_type': 'general'
            },
            {
                'subject': 'اجتماع فريق العمل الأسبوعي',
                'content': 'يُعقد اجتماع فريق العمل الأسبوعي يوم الأحد القادم في تمام الساعة 10:00 صباحاً.\n\nجدول الأعمال:\n1. مراجعة إنجازات الأسبوع\n2. مناقشة التحديات\n3. خطة الأسبوع القادم\n\nيرجى الحضور في الموعد المحدد.',
                'priority': 'normal',
                'message_type': 'notification'
            },
            {
                'subject': 'تنبيه: صيانة النظام',
                'content': 'سيتم إجراء صيانة دورية للنظام يوم الجمعة من الساعة 12:00 ظهراً حتى 2:00 ظهراً.\n\nخلال هذه الفترة قد تواجهون انقطاعاً في الخدمة.\n\nنعتذر عن أي إزعاج قد يحدث.',
                'priority': 'high',
                'message_type': 'alert'
            },
            {
                'subject': 'تهنئة بالإنجاز المتميز',
                'content': 'نتقدم بأحر التهاني لجميع أعضاء الفريق على الإنجاز المتميز في المشروع الأخير.\n\nلقد حققنا نتائج ممتازة تفوق التوقعات.\n\nشكراً لجهودكم المتواصلة وتفانيكم في العمل.',
                'priority': 'normal',
                'message_type': 'general'
            }
        ]
        
        # إنشاء الرسائل
        for i, msg_data in enumerate(sample_messages):
            # اختيار مرسل عشوائي
            sender = random.choice(users)
            
            # إنشاء الرسالة
            cursor.execute("""
                INSERT INTO message (subject, content, sender_id, priority, message_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                msg_data['subject'],
                msg_data['content'],
                sender[0],  # sender_id
                msg_data['priority'],
                msg_data['message_type'],
                datetime.now() - timedelta(days=random.randint(0, 7))  # تاريخ عشوائي في آخر أسبوع
            ))
            
            message_id = cursor.lastrowid
            
            # إضافة مستقبلين للرسالة
            recipients = [user for user in users if user[0] != sender[0]]  # جميع المستخدمين عدا المرسل
            
            # اختيار عدد عشوائي من المستقبلين (1-3)
            num_recipients = min(random.randint(1, 3), len(recipients))
            selected_recipients = random.sample(recipients, num_recipients)
            
            for recipient in selected_recipients:
                # تحديد حالة القراءة عشوائياً
                is_read = random.choice([True, False])
                read_at = datetime.now() - timedelta(hours=random.randint(1, 48)) if is_read else None
                
                cursor.execute("""
                    INSERT INTO message_recipient (message_id, recipient_id, is_read, read_at)
                    VALUES (?, ?, ?, ?)
                """, (message_id, recipient[0], is_read, read_at))
            
            print(f"تم إنشاء الرسالة: {msg_data['subject']}")
        
        # إنشاء بعض الردود
        cursor.execute("SELECT id, subject, sender_id FROM message LIMIT 3")
        messages_for_replies = cursor.fetchall()
        
        for original_msg in messages_for_replies:
            # اختيار مستخدم مختلف للرد
            replier = random.choice([user for user in users if user[0] != original_msg[2]])
            
            reply_content = f"شكراً لك على الرسالة.\n\nتم الاطلاع على المحتوى وسيتم اتخاذ الإجراءات اللازمة.\n\nمع تحياتي،\n{replier[1]}"
            
            # إنشاء رسالة الرد
            cursor.execute("""
                INSERT INTO message (subject, content, sender_id, parent_message_id, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                f"رد: {original_msg[1]}",
                reply_content,
                replier[0],
                original_msg[0],
                datetime.now() - timedelta(hours=random.randint(1, 24))
            ))
            
            reply_id = cursor.lastrowid
            
            # إضافة المرسل الأصلي كمستقبل للرد
            cursor.execute("""
                INSERT INTO message_recipient (message_id, recipient_id, is_read)
                VALUES (?, ?, ?)
            """, (reply_id, original_msg[2], random.choice([True, False])))
            
            print(f"تم إنشاء رد على الرسالة: {original_msg[1]}")
        
        # حفظ التغييرات
        conn.commit()
        print("تم إنشاء الرسائل التجريبية بنجاح!")
        
        # عرض إحصائيات
        cursor.execute("SELECT COUNT(*) FROM message")
        total_messages = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM message_recipient WHERE is_read = 0")
        unread_messages = cursor.fetchone()[0]
        
        print(f"\nإحصائيات:")
        print(f"إجمالي الرسائل: {total_messages}")
        print(f"الرسائل غير المقروءة: {unread_messages}")
        
    except Exception as e:
        print(f"خطأ في إنشاء الرسائل التجريبية: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_sample_messages()
