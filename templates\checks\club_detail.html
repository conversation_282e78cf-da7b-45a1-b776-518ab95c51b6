{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">تشيكات نادي {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('checks_clubs_list') }}">قائمة الأندية</a></li>
                <li class="breadcrumb-item active" aria-current="page">تشيكات نادي {{ club.name }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('new_check', club_id=club.id) }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> تشيك جديد
        </a>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <form method="get" action="{{ url_for('checks_club_detail', club_id=club.id) }}" class="row g-2">
                    <div class="col-md-10">
                        <input type="text" name="search" class="form-control form-control-sm" placeholder="البحث في التشيكات..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-sm btn-primary w-100">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <i class="fas fa-clipboard-check me-1"></i> قائمة تشيكات نادي {{ club.name }}
            </div>
            <div class="card-body p-0">
                {% if checks %}
                <div class="table-responsive">
                    <table class="table table-sm mb-0">
                        <thead>
                            <tr>
                                <th class="text-center">#</th>
                                <th class="text-center">التاريخ</th>
                                <th class="text-center">النسبة</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in checks %}
                            <tr>
                                <td class="text-center py-2">{{ loop.index }}</td>
                                <td class="text-center py-2">{{ check.check_date.strftime('%Y-%m-%d') }}</td>
                                <td class="text-center py-2">
                                    {% set compliance_percentage = check.get_compliance_percentage() %}
                                    <div class="progress" style="height: 25px; background-color: #e9ecef; position: relative;">
                                        <div class="progress-bar {% if compliance_percentage < 50 %}bg-danger{% elif compliance_percentage < 80 %}bg-warning{% else %}bg-success{% endif %}" role="progressbar" style="width: {{ compliance_percentage }}%; min-width: 2em;"></div>
                                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center;">
                                            <span style="color: white; font-weight: bold; font-size: 18px; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">{{ compliance_percentage }}%</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center py-2">
                                    <a href="{{ url_for('check_detail', id=check.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('edit_check', id=check.id) }}" class="btn btn-sm btn-warning py-1 px-2" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('delete_check', id=check.id) }}" class="btn btn-sm btn-danger py-1 px-2" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info py-2 mb-0">
                    لا توجد تشيكات مسجلة لهذا النادي حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
