from app import app, db, GoogleMapsRating
from datetime import date, timedelta

if __name__ == '__main__':
    with app.app_context():
        try:
            # إنشاء الجدول الجديد
            db.create_all()
            print("تم إنشاء جدول تقييمات Google Maps بنجاح!")
            
            # إضافة بعض البيانات التجريبية
            sample_ratings = [
                {
                    'club_id': 1,
                    'date': date.today() - timedelta(days=30),
                    'rating_count': 145,
                    'rating_average': 4.2
                },
                {
                    'club_id': 1,
                    'date': date.today() - timedelta(days=15),
                    'rating_count': 152,
                    'rating_average': 4.3
                },
                {
                    'club_id': 1,
                    'date': date.today(),
                    'rating_count': 158,
                    'rating_average': 4.4
                },
                {
                    'club_id': 2,
                    'date': date.today() - timedelta(days=20),
                    'rating_count': 89,
                    'rating_average': 3.8
                },
                {
                    'club_id': 2,
                    'date': date.today() - timedelta(days=5),
                    'rating_count': 95,
                    'rating_average': 3.9
                },
                {
                    'club_id': 3,
                    'date': date.today() - timedelta(days=10),
                    'rating_count': 203,
                    'rating_average': 4.6
                },
            ]
            
            # إضافة البيانات التجريبية
            for rating_data in sample_ratings:
                # التحقق من عدم وجود تقييم مسبق لنفس النادي والتاريخ
                existing = GoogleMapsRating.query.filter_by(
                    club_id=rating_data['club_id'],
                    date=rating_data['date']
                ).first()
                
                if not existing:
                    rating = GoogleMapsRating(
                        club_id=rating_data['club_id'],
                        date=rating_data['date'],
                        rating_count=rating_data['rating_count'],
                        rating_average=rating_data['rating_average']
                    )
                    db.session.add(rating)
            
            db.session.commit()
            print("تم إضافة البيانات التجريبية بنجاح!")
            
            # عرض البيانات المضافة
            ratings = GoogleMapsRating.query.all()
            print(f"تم إضافة {len(ratings)} تقييم:")
            for rating in ratings:
                print(f"- النادي {rating.club_id}: {rating.rating_count} تقييم، متوسط {rating.rating_average} في {rating.date}")
                
        except Exception as e:
            print(f"حدث خطأ: {str(e)}")
