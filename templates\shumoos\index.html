{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary mb-0"><i class="fas fa-sun me-2"></i> سجل شموس</h2>
        <div>
            <a href="{{ url_for('new_shumoos') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة سجل جديد
            </a>
        </div>
    </div>

    <!-- نموذج البحث -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="get" action="{{ url_for('shumoos_list') }}" class="row g-3">
                <div class="col-md-10">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" placeholder="البحث في اسم النادي...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
                <div class="col-md-2 text-start">
                    {% if search_query %}
                    <a href="{{ url_for('shumoos_list') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء البحث
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الأندية -->
    <div class="card shadow-lg">
        <div class="card-header bg-white py-3">
            <h4 class="mb-0 text-primary"><i class="fas fa-list me-2"></i> قائمة الأندية <span class="badge bg-primary rounded-pill ms-2">{{ clubs_with_latest_record|length }}</span></h4>
        </div>
        <div class="card-body p-0">
            {% if clubs_with_latest_record %}
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">#</th>
                            <th style="font-size: 18px; color: #ffffff; background-color: #0d6efd; font-weight: bold;">النادي</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd; font-weight: bold;">آخر عدد مسجل</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">عدد السجلات</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">آخر تحديث</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in clubs_with_latest_record %}
                        <tr>
                            <td class="text-center">{{ loop.index }}</td>
                            <td>{{ item.club.name }}</td>
                            <td class="text-center english-number" style="font-weight: bold;">
                                {% if item.latest_record %}
                                    {{ item.latest_record.registered_count }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="text-center english-number" style="font-weight: bold;">
                                {{ item.record_count }}
                            </td>
                            <td class="text-center english-number" style="font-weight: bold;">
                                {% if item.latest_record %}
                                    {{ item.latest_record.registration_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="{{ url_for('shumoos_club_records', club_id=item.club.id) }}" class="btn btn-sm btn-info" title="عرض السجلات">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('new_shumoos') }}?club_id={{ item.club.id }}" class="btn btn-sm btn-success" title="إضافة سجل جديد">
                                        <i class="fas fa-plus-circle"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أندية للعرض</h5>
                <p class="text-muted">قم بإضافة نادي جديد أولاً</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
