{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mb-1">الموظفين</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الموظفين</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if current_user.has_permission('add_employee') %}
            <a href="{{ url_for('new_employee') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة موظف جديد
            </a>
            <a href="{{ url_for('import_employees_excel') }}" class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i> استيراد من إكسل
            </a>
            <a href="{{ url_for('download_employee_template') }}" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i> تنزيل القالب
            </a>
            {% endif %}
        </div>
    </div>

    <!-- البحث والتصفية -->
    <div class="card mb-4 shadow">
        <div class="card-header bg-light py-3">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i> بحث وتصفية</h5>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('employees') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text fs-6"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control form-control-sm fs-6" name="search" placeholder="بحث..." value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm fs-6" name="club">
                            <option value="">-- النادي --</option>
                            {% for club in clubs %}
                            <option value="{{ club.id }}" {% if club_filter == club.id|string %}selected{% endif %}>{{ club.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm fs-6" name="role">
                            <option value="">-- الدور الوظيفي --</option>
                            {% for role in roles %}
                            <option value="{{ role }}" {% if role_filter == role %}selected{% endif %}>{{ role }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm fs-6" name="status">
                            <option value="">-- الحالة --</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm fs-6" name="trainer_type">
                            <option value="">-- حالة المدرب --</option>
                            <option value="متفرغ" {% if trainer_type_filter == 'متفرغ' %}selected{% endif %}>متفرغ</option>
                            <option value="غير متفرغ" {% if trainer_type_filter == 'غير متفرغ' %}selected{% endif %}>غير متفرغ</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary py-2 fs-6 flex-grow-1">تطبيق</button>
                            {% if search_query or club_filter or role_filter or status_filter or trainer_type_filter %}
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary py-2 fs-6">
                                <i class="fas fa-times me-1"></i> عرض الكل
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الموظفين -->
    <div class="card shadow-lg" >
        <div class="card-header bg-white py-3">
            <h4 class="mb-0 text-primary">
                <i class="fas fa-users me-2"></i> قائمة الموظفين
                <span class="badge bg-primary rounded-pill ms-2">{{ employees|length }}</span>
                {% if search_query or club_filter or role_filter or status_filter or trainer_type_filter %}
                <span class="badge bg-warning text-dark ms-2">
                    <i class="fas fa-filter me-1"></i> تم التصفية
                </span>
                <div class="mt-2 small text-muted">
                    {% if search_query %}<span class="badge bg-light text-dark me-1"><i class="fas fa-search me-1"></i> {{ search_query }}</span>{% endif %}
                    {% if club_filter %}
                        {% for club in clubs %}
                            {% if club.id|string == club_filter %}
                            <span class="badge bg-light text-dark me-1"><i class="fas fa-building me-1"></i> {{ club.name }}</span>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    {% if role_filter %}<span class="badge bg-light text-dark me-1"><i class="fas fa-user-tag me-1"></i> {{ role_filter }}</span>{% endif %}
                    {% if status_filter %}<span class="badge bg-light text-dark me-1"><i class="fas fa-toggle-on me-1"></i> {% if status_filter == 'active' %}نشط{% else %}غير نشط{% endif %}</span>{% endif %}
                    {% if trainer_type_filter %}<span class="badge bg-light text-dark me-1"><i class="fas fa-clock me-1"></i> {{ trainer_type_filter }}</span>{% endif %}
                </div>
                {% endif %}
            </h4>
        </div>
        <div class="card-body p-0">
            {% if employees %}
            <div class="table-responsive">
                <table class="table table-striped table-hover align-middle" style="font-size: 0.85rem;">
                    <thead class="table-primary">
                        <tr>
                            <th class="fw-bold text-center">الرقم الوظيفي</th>
                            <th class="fw-bold">اسم الموظف</th>
                            <th class="fw-bold">الوظيفة</th>
                            <th class="fw-bold">الدور الوظيفي</th>
                            <th class="fw-bold">النادي</th>
                            <th class="fw-bold">الحالة</th>
                            <th class="fw-bold text-center">حالة المدرب</th>
                            <th class="text-center fw-bold">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr class="py-3" style="height: 40px;">
                            <td class="text-center">{{ employee.employee_id }}</td>
                            <td>{{ employee.name }}</td>
                            <td>{{ employee.position }}</td>
                            <td>{{ employee.role }}</td>
                            <td>{{ employee.club.name }}</td>
                            <td>
                                {% if employee.is_active %}
                                <span class="badge bg-success px-3 py-2">نشط</span>
                                {% else %}
                                <span class="badge bg-danger px-3 py-2">غير نشط</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if employee.position == 'مدرب' and employee.trainer_type %}
                                    {% if employee.trainer_type == 'متفرغ' %}
                                    <span class="badge bg-success px-2 py-1">متفرغ</span>
                                    {% else %}
                                    <span class="badge bg-secondary px-2 py-1">غير متفرغ</span>
                                    {% endif %}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group ">
                                    <a href="{{ url_for('employee_detail', id=employee.id) }}" class="btn btn-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('edit_employee') %}
                                    <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if current_user.has_permission('delete_employee') %}
                                    <a href="{{ url_for('delete_employee', id=employee.id) }}" class="btn btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف الموظف \'{{ employee.name }}\' ؟')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <div class="display-1 text-muted mb-3">
                    <i class="fas fa-user-slash"></i>
                </div>
                <h5>لا يوجد موظفين</h5>
                <p class="text-muted">يمكنك إضافة موظفين جدد أو استيراد بيانات الموظفين من ملف إكسل</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>


{% endblock %}
