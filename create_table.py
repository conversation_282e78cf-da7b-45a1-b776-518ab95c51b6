import sqlite3

# الاتصال بقاعدة البيانات
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

# إنشاء جدول employee_schedule
cursor.execute('''
CREATE TABLE IF NOT EXISTS employee_schedule (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id INTEGER NOT NULL,
    club_id INTEGER NOT NULL,
    shift1_start TEXT,
    shift1_end TEXT,
    shift2_start TEXT,
    shift2_end TEXT,
    mobile_number TEXT,
    work_days TEXT,
    off_days TEXT,
    allocation_from TEXT,
    allocation_to TEXT,
    allocation_day TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (employee_id) REFERENCES employee (id),
    FOREI<PERSON><PERSON> KEY (club_id) REFERENCES club (id)
)
''')

# حفظ التغييرات وإغلاق الاتصال
conn.commit()
conn.close()

print("تم إنشاء جدول employee_schedule بنجاح!")
