/* تنسيق مخصص لتكبير التقويم */

/* تنسيق حجم التقويم */
::-webkit-calendar-picker {
    font-size: 14px !important;
    scale: 1.2;
    transform-origin: top right;
}

/* تنسيق حجم الخط في أيام الأسبوع */
::-webkit-calendar-picker-weekday-row {
    font-size: 14px !important;
    font-weight: bold !important;
}

/* تنسيق حجم الخط في أيام الشهر */
::-webkit-calendar-picker-day-cell {
    font-size: 14px !important;
}

/* تنسيق حجم الخط في عنوان الشهر والسنة */
::-webkit-calendar-picker-title {
    font-size: 16px !important;
    font-weight: bold !important;
}

/* تنسيق حجم أزرار التنقل */
::-webkit-calendar-picker-navigation-button {
    font-size: 18px !important;
}

/* تنسيق حجم الخط في أزرار اليوم والشهر */
::-webkit-calendar-picker-footer-button {
    font-size: 14px !important;
    font-weight: bold !important;
}

/* تنسيق عام للتقويم */
::-webkit-datetime-edit {
    font-size: 16px !important;
}

/* تنسيق حجم التقويم باستخدام CSS العام */
.calendar-container {
    transform: scale(1.2);
    transform-origin: top right;
}

/* تنسيق مخصص للتقويم باستخدام JavaScript */
.calendar-large {
    transform: scale(1.2);
    transform-origin: top right;
}

/* تنسيق حجم الخط في التقويم */
.calendar-day {
    font-size: 14px !important;
}

.calendar-weekday {
    font-size: 14px !important;
    font-weight: bold !important;
}

.calendar-month-year {
    font-size: 16px !important;
    font-weight: bold !important;
}

/* تنسيق للتقويم المنبثق */
input[type="date"]::-webkit-calendar-picker-indicator:hover + ::-webkit-datetime-edit-fields-wrapper {
    background-color: transparent;
}

/* تكبير حجم التقويم المنبثق */
::-webkit-datetime-edit-fields-wrapper:focus {
    font-size: 20px !important;
}

/* تنسيق عام للتقويم المنبثق */
.date-picker-popup {
    font-size: 18px !important;
    transform: scale(1.5);
    transform-origin: top right;
}
