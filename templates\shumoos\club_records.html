{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-primary mb-0"><i class="fas fa-sun me-2"></i> سجلات شموس - {{ club.name }}</h2>
            <p class="text-muted mt-2">عدد السجلات: {{ shumoos_records|length }}</p>
        </div>
        <div>
            <a href="{{ url_for('new_shumoos') }}?club_id={{ club.id }}" class="btn btn-success me-2">
                <i class="fas fa-plus-circle me-1"></i> إضافة سجل جديد
            </a>
            <a href="{{ url_for('shumoos_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الأندية
            </a>
        </div>
    </div>

    <!-- جدول السجلات -->
    <div class="card shadow-lg">
        <div class="card-header bg-white py-3">
            <h4 class="mb-0 text-primary"><i class="fas fa-list me-2"></i> سجلات النادي</h4>
        </div>
        <div class="card-body p-0">
            {% if shumoos_records %}
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">#</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd; font-weight: bold;">العدد المسجل</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">الفارق</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">تاريخ التسجيل</th>
                            <th class="text-center" style="font-size: 18px; color: #ffffff; background-color: #0d6efd;font-weight: bold;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in shumoos_records %}
                        <tr>
                            <td class="text-center">{{ loop.index }}</td>
                            <td class="text-center english-number" style="font-weight: bold; ">{{ record.registered_count }}</td>
                            <td class="text-center english-number" style="font-weight: bold; 
                                {% if record.get_difference() > 0 %}
                                    color: green;
                                {% elif record.get_difference() < 0 %}
                                    color: red;
                                {% endif %}
                            ">
                                {% if record.get_difference() > 0 %}
                                    +{{ record.get_difference() }}
                                {% else %}
                                    {{ record.get_difference() }}
                                {% endif %}
                            </td>
                            <td class="text-center english-number" style="font-weight: bold; ">{{ record.registration_date.strftime('%Y-%m-%d') }}</td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="{{ url_for('shumoos_record_detail', id=record.id) }}" class="btn btn-sm btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('edit_shumoos', id=record.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{{ url_for('delete_shumoos', id=record.id) }}" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟');">
                                        <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-sun fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد سجلات شموس لهذا النادي</h5>
                <p class="text-muted">قم بإضافة سجل جديد للبدء</p>
                <a href="{{ url_for('new_shumoos') }}?club_id={{ club.id }}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus-circle me-1"></i> إضافة سجل جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
