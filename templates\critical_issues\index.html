{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">سجل الأعطال الحرجة</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item active" aria-current="page">الأعطال الحرجة</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('new_critical_issue') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة عطل جديد
        </a>
    </div>
</div>

<!-- بحث وتصفية -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form method="get" action="{{ url_for('critical_issues_list') }}">
            <div class="row align-items-end">
                <div class="col-md-10">
                    <label for="search" class="form-label">بحث في الأندية</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="اسم النادي..." value="{{ search_query }}">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الأندية -->
{% if clubs_with_stats %}
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> قائمة الأندية</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="30%">اسم النادي</th>
                        <th class="text-center" width="10%">إجمالي الأعطال</th>
                        <th class="text-center" width="10%">معلقة</th>
                        <th class="text-center" width="10%">متأخرة</th>
                        <th class="text-center" width="10%">مكتملة</th>
                        <th class="text-center" width="10%">مغلقة</th>
                        <th class="text-center" width="15%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for club_data in clubs_with_stats %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building text-primary me-2"></i>
                                <strong>{{ club_data.club.name }}</strong>
                            </div>
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.total > 0 %}
                                <span class="badge bg-info">{{ club_data.stats.total }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.pending > 0 %}
                                <span class="badge bg-warning text-dark">{{ club_data.stats.pending }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.overdue > 0 %}
                                <span class="badge bg-danger">{{ club_data.stats.overdue }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.completed > 0 %}
                                <span class="badge bg-success">{{ club_data.stats.completed }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if club_data.stats.closed > 0 %}
                                <span class="badge bg-secondary">{{ club_data.stats.closed }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('critical_issues_club_detail', club_id=club_data.club.id) }}" class="btn btn-sm btn-primary" title="عرض أعطال النادي">
                                    <i class="fas fa-list-alt me-1"></i> عرض الأعطال
                                </a>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="تصدير Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" style="min-width: 300px;">
                                        <li><h6 class="dropdown-header">تصدير الأعطال الحرجة</h6></li>
                                        <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club_data.club.id) }}">
                                            <i class="fas fa-download me-1"></i> جميع الأعطال
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club_data.club.id, month='current') }}">
                                            <i class="fas fa-calendar-alt me-1"></i> الشهر الحالي
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">تصدير شهر محدد</h6></li>
                                        <li class="px-3 py-2">
                                            <form method="GET" action="{{ url_for('export_critical_issues_excel', club_id=club_data.club.id) }}" class="export-form">
                                                <div class="row g-2">
                                                    <div class="col-7">
                                                        <select class="form-select form-select-sm" name="specific_month" required>
                                                            <option value="">اختر الشهر</option>
                                                            <option value="1">يناير</option>
                                                            <option value="2">فبراير</option>
                                                            <option value="3">مارس</option>
                                                            <option value="4">أبريل</option>
                                                            <option value="5">مايو</option>
                                                            <option value="6">يونيو</option>
                                                            <option value="7">يوليو</option>
                                                            <option value="8">أغسطس</option>
                                                            <option value="9">سبتمبر</option>
                                                            <option value="10">أكتوبر</option>
                                                            <option value="11">نوفمبر</option>
                                                            <option value="12">ديسمبر</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-5">
                                                        <select class="form-select form-select-sm" name="year" required>
                                                            <option value="">العام</option>
                                                            {% set current_year = 2024 %}
                                                            {% for year in range(2020, 2030) %}
                                                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <button type="submit" class="btn btn-primary btn-sm w-100">
                                                        <i class="fas fa-download me-1"></i> تصدير
                                                    </button>
                                                </div>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                <a href="{{ url_for('new_critical_issue') }}?club_id={{ club_data.club.id }}" class="btn btn-sm btn-warning" title="إضافة عطل جديد">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
    <div class="card">
        <div class="card-body">
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أندية للعرض</h5>
                <p class="text-muted">لا يوجد لديك أندية متاحة أو لا توجد أندية تطابق البحث</p>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block head %}
<style>
    /* تحسين مظهر بطاقات النوادي */
    .card-header.bg-primary {
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
        border-bottom: 3px solid rgba(255, 255, 255, 0.2);
    }

    .card-header h5 a:hover {
        text-decoration: underline !important;
    }

    /* تحسين مظهر الشارات */
    .badge {
        font-size: 0.75rem;
        padding: 0.4em 0.6em;
        border-radius: 0.375rem;
    }

    .badge.bg-light {
        border: 1px solid #dee2e6;
    }

    /* تحسين مظهر الجداول */
    .table th {
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    /* تحسين مظهر الأزرار */
    .btn-group .btn {
        border-radius: 0.25rem;
        margin: 0 1px;
    }

    /* تحسين مظهر التواريخ المتأخرة */
    .text-danger.fw-bold {
        background: linear-gradient(45deg, #dc3545, #b02a37);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    /* تحسين مظهر الأيقونات */
    .fas.fa-exclamation-triangle {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* تحسين مظهر البطاقات */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تحسين مظهر الصفوف */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر الشارات في الرأس */
    .card-header .badge {
        font-size: 0.7rem;
        font-weight: 500;
    }

    /* تحسين المسافات */
    .gap-2 {
        gap: 0.5rem !important;
    }

    /* تحسين أيقونة الـ collapse */
    .collapse-icon {
        transition: transform 0.3s ease;
        font-size: 0.8rem;
    }

    .card-header[aria-expanded="false"] .collapse-icon {
        transform: rotate(-90deg);
    }

    /* تحسين مظهر الرأس القابل للنقر */
    .card-header[data-bs-toggle="collapse"]:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
    }

    /* تحسين مظهر بطاقات الملخص */
    .card.bg-primary,
    .card.bg-info,
    .card.bg-warning,
    .card.bg-danger {
        border: none;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    .card.bg-primary:hover,
    .card.bg-info:hover,
    .card.bg-warning:hover,
    .card.bg-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    /* تحسين نموذج التصدير */
    .export-form {
        background: #f8f9fc;
        border-radius: 0.375rem;
        padding: 0.5rem;
        margin: 0;
    }

    .export-form .form-select-sm {
        font-size: 0.75rem;
        border: 1px solid #d1d3e2;
        border-radius: 0.25rem;
    }

    .export-form .form-select-sm:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .export-form .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .export-form .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.3);
    }

    /* تحسين رؤوس القائمة المنسدلة */
    .dropdown-header {
        font-size: 0.75rem;
        font-weight: 600;
        color: #495057;
        background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
        border-radius: 0.25rem;
        margin: 0.25rem 0.5rem;
        padding: 0.5rem;
    }

    /* تحسين الفواصل */
    .dropdown-divider {
        margin: 0.5rem 0;
        border-color: #e9ecef;
    }

    /* تحسين تفاعل القائمة المنسدلة */
    .dropdown-item {
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateX(5px);
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // إضافة تأثيرات hover للبطاقات
        $('.card.bg-primary, .card.bg-info, .card.bg-warning, .card.bg-danger').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // إضافة tooltip للشارات
        $('[title]').tooltip();

        // تحسين تفاعل الجدول
        $('.table tbody tr').hover(
            function() {
                $(this).addClass('table-active');
            },
            function() {
                $(this).removeClass('table-active');
            }
        );

        // تحسين تفاعل القائمة المنسدلة
        $('.dropdown-toggle').on('show.bs.dropdown', function() {
            $(this).addClass('shadow-lg');
        }).on('hide.bs.dropdown', function() {
            $(this).removeClass('shadow-lg');
        });

        // إضافة تأثير تحميل للنماذج
        $('.export-form').on('submit', function() {
            const $btn = $(this).find('button[type="submit"]');
            const originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري التصدير...');
            $btn.prop('disabled', true);

            // استعادة النص الأصلي بعد 5 ثوان (في حالة عدم نجاح التحميل)
            setTimeout(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }, 5000);
        });
    });
</script>
{% endblock %}
