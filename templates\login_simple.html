{% extends "base.html" %}

{% block head %}
<style>
    .login-form {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .login-input {
        font-size: 24px !important;
        height: 60px !important;
        padding: 10px !important;
        text-align: center !important;
    }
    
    .password-container {
        position: relative;
    }
    
    .password-toggle {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        font-size: 20px;
        z-index: 10;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/Bodymasters-Logo.jpg') }}" alt="شركة الأندية للرياضة" class="img-fluid" style="max-width: 300px;">
            </div>
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">تسجيل الدخول</h4>
                </div>
                <div class="card-body">
                    <form action="" method="post" novalidate class="login-form">
                        <div class="mb-3">
                            <label for="username" class="form-label">الرقم الوظيفي</label>
                            <input type="text" class="form-control login-input" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="password-container">
                                <input type="password" class="form-control login-input" id="password" name="password" required>
                                <span class="password-toggle" onclick="togglePassword()">
                                    <i class="fas fa-eye fa-lg" id="togglePassword"></i>
                                </span>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">تذكرني</label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function togglePassword() {
        var passwordInput = document.getElementById('password');
        var toggleIcon = document.getElementById('togglePassword');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash fa-lg';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye fa-lg';
        }
    }
</script>
{% endblock %}
