{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">مخالفات {{ employee.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_clubs_list') }}">سجل المخالفات</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_club_detail', club_id=employee.club_id) }}">{{ employee.club.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ employee.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        {% if current_user.has_permission('add_violation') %}
        <a href="{{ url_for('new_violation_for_employee', employee_id=employee.id) }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> تسجيل مخالفة جديدة
        </a>
        {% endif %}
        <a href="{{ url_for('violations_club_detail', club_id=employee.club_id) }}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    {{ employee.name }}
                </h5>
                <small>{{ employee.club.name }}</small>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-id-badge me-1"></i>
                        {{ employee.employee_id }}
                    </span>
                    <span class="badge bg-warning text-dark">
                        <i class="fas fa-briefcase me-1"></i>
                        {{ employee.role }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h4 class="mb-0">
                        {% set total_violations = 0 %}
                        {% for month_data in violations_by_month %}
                            {% set total_violations = total_violations + month_data.violations|length %}
                        {% endfor %}
                        {{ total_violations }}
                    </h4>
                    <small class="text-muted">إجمالي المخالفات</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <i class="fas fa-calendar-alt fa-2x text-danger mb-2"></i>
                    <h4 class="mb-0">
                        {% if violations_by_month %}
                            {{ violations_by_month[0].violations|length }}
                        {% else %}
                            0
                        {% endif %}
                    </h4>
                    <small class="text-muted">{{ current_month_name }}</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-0">
                        {% set signed_count = 0 %}
                        {% for month_data in violations_by_month %}
                            {% for violation in month_data.violations %}
                                {% if violation.is_signed %}
                                    {% set signed_count = signed_count + 1 %}
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                        {{ signed_count }}
                    </h4>
                    <small class="text-muted">مخالفات موقعة</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <i class="fas fa-times-circle fa-2x text-secondary mb-2"></i>
                    <h4 class="mb-0">
                        {% set unsigned_count = total_violations - signed_count %}
                        {{ unsigned_count }}
                    </h4>
                    <small class="text-muted">مخالفات غير موقعة</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الفلترة -->
<div class="mb-4">
    <div class="btn-group w-100">
        <a href="{{ url_for('violations_employee_detail', employee_id=employee.id, filter='current_month') }}" class="btn {% if filter_type == 'current_month' %}btn-primary{% else %}btn-outline-primary{% endif %}">
            <i class="fas fa-calendar-alt me-1"></i> عرض مخالفات الشهر الحالي ({{ current_month_name }})
        </a>
        <a href="{{ url_for('violations_employee_detail', employee_id=employee.id, filter='all') }}" class="btn {% if filter_type == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">
            <i class="fas fa-list me-1"></i> عرض الكل
        </a>
    </div>
</div>

<!-- قائمة المخالفات حسب الشهر -->
{% for month_data in violations_by_month %}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i> مخالفات شهر {{ month_data.month_name }} <span class="badge bg-primary rounded-pill ms-2">{{ month_data.violations|length }}</span></h5>
    </div>
    <div class="card-body p-0">
        {% if month_data.violations %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="20%">نوع المخالفة</th>
                        <th class="text-center" width="10%">رقم المخالفة</th>
                        <th class="text-center" width="12%">رقم المخالفة من نفس النوع</th>
                        <th class="text-center" width="12%">تاريخ المخالفة</th>
                        <th width="15%">مصدر المخالفة</th>
                        <th class="text-center" width="8%">التوقيع</th>
                        <th class="text-center" width="8%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for violation in month_data.violations %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>{{ violation.violation_type.name }}</td>
                        <td class="text-center english-number">{{ violation.violation_number }}</td>
                        <td class="text-center english-number">{{ violation.same_type_number }}</td>
                        <td class="text-center english-number">{{ violation.violation_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ violation.violation_source }}</td>
                        <td class="text-center">
                            {% if violation.is_signed %}
                            <span class="badge bg-success">تم التوقيع</span>
                            {% else %}
                            <span class="badge bg-danger">لم يتم التوقيع</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <a href="{{ url_for('violation_detail', id=violation.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if current_user.has_permission('edit_violation') %}
                            <a href="{{ url_for('edit_violation', id=violation.id) }}" class="btn btn-sm btn-warning py-1 px-2" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% endif %}
                            {% if current_user.has_permission('delete_violation') %}
                            <form method="POST" action="{{ url_for('delete_violation', id=violation.id) }}" style="display:inline;">
                                <button type="submit" class="btn btn-sm btn-danger py-1 px-2" title="حذف"
                                        onclick="return confirm('هل أنت متأكد من حذف مخالفة {{ violation.violation_type.name }}؟')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <p class="text-muted mb-0">لا توجد مخالفات في هذا الشهر</p>
        </div>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لا توجد مخالفات مسجلة لهذا الموظف</h5>
        {% if current_user.has_permission('add_violation') %}
        <a href="{{ url_for('new_violation_for_employee', employee_id=employee.id) }}" class="btn btn-primary mt-2">
            <i class="fas fa-plus-circle me-1"></i> تسجيل مخالفة جديدة
        </a>
        {% endif %}
    </div>
</div>
{% endfor %}


{% endblock %}

{% block head %}
<style>
    /* تحسين مظهر بطاقة الموظف */
    .card-header.bg-primary {
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
        border-bottom: 3px solid rgba(255, 255, 255, 0.2);
    }

    /* تحسين مظهر الشارات */
    .badge {
        font-size: 0.75rem;
        padding: 0.4em 0.6em;
        border-radius: 0.375rem;
    }

    .badge.bg-light {
        border: 1px solid #dee2e6;
    }

    /* تحسين مظهر الجداول */
    .table th {
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    /* تحسين مظهر البطاقات */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تحسين مظهر الصفوف */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر الشارات في الرأس */
    .card-header .badge {
        font-size: 0.7rem;
        font-weight: 500;
    }

    /* تحسين مظهر الإحصائيات */
    .text-center i {
        opacity: 0.8;
    }

    .text-center h4 {
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    /* تحسين مظهر أزرار الفلترة */
    .btn-group .btn {
        border-radius: 0.375rem;
    }

    .btn-group .btn:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .btn-group .btn:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    /* تحسين المسافات */
    .gap-2 {
        gap: 0.5rem !important;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    function registerViolation(employeeId, employeeNumber, employeeName, employeeRole, clubName) {
        // إنشاء نموذج مؤقت
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("new_violation") }}';
        form.style.display = 'none';

        // إضافة حقول النموذج
        const fields = {
            'employee_id': employeeNumber,
            'employee_id_hidden': employeeNumber,
            'employee_name_hidden': employeeName,
            'employee_role_hidden': employeeRole,
            'club_name_hidden': clubName,
            'auto_fill_js': 'true',
            'employee_data_js': JSON.stringify({
                id: employeeId,
                employee_id: employeeNumber,
                name: employeeName,
                role: employeeRole,
                club_name: clubName
            })
        };

        // إضافة الحقول إلى النموذج
        for (const key in fields) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = fields[key];
            form.appendChild(input);
        }

        // إضافة النموذج إلى الصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }


</script>
{% endblock %}
