{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">إنشاء حساب جديد</h4>
            </div>
            <div class="card-body">
                <form action="" method="post" novalidate>
                    <div class="mb-3">
                        <label for="username" class="form-label">الرقم الوظيفي</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">النوادي</label>
                        <div class="row">
                            {% for club in clubs %}
                            <div class="col-md-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="club_{{ club.id }}" name="clubs" value="{{ club.id }}">
                                    <label class="form-check-label" for="club_{{ club.id }}">
                                        {{ club.name }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">تسجيل</button>
                    </div>
                </form>
                <div class="mt-3 text-center">
                    <p>لديك حساب بالفعل؟ <a href="{{ url_for('login') }}">تسجيل الدخول</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
