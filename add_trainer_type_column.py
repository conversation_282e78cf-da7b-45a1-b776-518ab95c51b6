#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإضافة عمود نوع المدرب إلى جدول الموظفين
"""

import sqlite3
import os

def add_trainer_type_column():
    """إضافة عمود trainer_type إلى جدول employee"""

    # مسار قاعدة البيانات
    db_path = 'app.db'

    if not os.path.exists(db_path):
        print(f"❌ ملف قاعدة البيانات غير موجود: {db_path}")
        return False

    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود العمود مسبقاً
        cursor.execute("PRAGMA table_info(employee)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'trainer_type' in columns:
            print("✅ عمود trainer_type موجود بالفعل في جدول employee")
            conn.close()
            return True

        # إضافة العمود الجديد
        cursor.execute("""
            ALTER TABLE employee
            ADD COLUMN trainer_type VARCHAR(20) DEFAULT NULL
        """)

        conn.commit()
        print("✅ تم إضافة عمود trainer_type بنجاح إلى جدول employee")

        # التحقق من إضافة العمود
        cursor.execute("PRAGMA table_info(employee)")
        columns = cursor.fetchall()

        print("\n📋 أعمدة جدول employee بعد التحديث:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")

        conn.close()
        return True

    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == '__main__':
    print("🔄 بدء إضافة عمود نوع المدرب...")
    success = add_trainer_type_column()

    if success:
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("💡 يمكنك الآن استخدام حقل نوع المدرب في النماذج")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
