{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">حذف متابعة الكاميرات</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('camera_checks_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-danger text-white py-2">
                <i class="fas fa-exclamation-triangle me-1"></i> تأكيد الحذف
            </div>
            <div class="card-body">
                <p class="mb-4">هل أنت متأكد من حذف متابعة الكاميرات رقم {{ camera_check.id }} لنادي {{ camera_check.club.name }}؟</p>

                <form action="{{ url_for('delete_camera_check', id=camera_check.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> نعم، حذف المتابعة
                    </button>
                    <a href="{{ url_for('camera_checks_list') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
