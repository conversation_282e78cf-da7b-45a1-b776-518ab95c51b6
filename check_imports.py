#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("فحص الاستيرادات...")

try:
    print("1. فحص Flask...")
    from flask import Flask
    print("✓ Flask")
    
    print("2. فحص SQLAlchemy...")
    from flask_sqlalchemy import SQLAlchemy
    print("✓ SQLAlchemy")
    
    print("3. فحص Flask-Login...")
    from flask_login import LoginManager
    print("✓ Flask-Login")
    
    print("4. فحص config...")
    from config import get_database_uri, SECRET_KEY
    print("✓ Config")
    
    print("5. فحص datetime...")
    from datetime import datetime, date
    print("✓ datetime")
    
    print("6. فحص os...")
    import os
    print("✓ os")
    
    print("7. فحص uuid...")
    import uuid
    print("✓ uuid")
    
    print("8. فحص werkzeug...")
    from werkzeug.utils import secure_filename
    print("✓ werkzeug")
    
    print("\nجميع الاستيرادات تعمل بشكل صحيح!")
    
    print("\n9. فحص قاعدة البيانات...")
    if os.path.exists('app.db'):
        print("✓ قاعدة البيانات موجودة")
    else:
        print("✗ قاعدة البيانات غير موجودة")
    
    print("\n10. فحص مجلد uploads...")
    if os.path.exists('uploads'):
        print("✓ مجلد uploads موجود")
    else:
        print("✗ مجلد uploads غير موجود")
    
    print("\n11. فحص مجلد templates...")
    if os.path.exists('templates'):
        print("✓ مجلد templates موجود")
    else:
        print("✗ مجلد templates غير موجود")
        
    print("\n12. فحص مجلد templates/messages...")
    if os.path.exists('templates/messages'):
        print("✓ مجلد templates/messages موجود")
    else:
        print("✗ مجلد templates/messages غير موجود")
    
except ImportError as e:
    print(f"✗ خطأ في الاستيراد: {e}")
except Exception as e:
    print(f"✗ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("\nانتهى الفحص.")
