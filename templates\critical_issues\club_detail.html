{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">أعطال {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('critical_issues_list') }}">الأعطال الحرجة</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ club.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-excel me-1"></i> تصدير Excel
            </button>
            <ul class="dropdown-menu dropdown-menu-end" style="min-width: 300px;">
                <li><h6 class="dropdown-header">تصدير الأعطال الحرجة</h6></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id) }}">
                    <i class="fas fa-download me-1"></i> جميع الأعطال
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id, month='current') }}">
                    <i class="fas fa-calendar-alt me-1"></i> الشهر الحالي
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">تصدير شهر محدد</h6></li>
                <li class="px-3 py-2">
                    <form method="GET" action="{{ url_for('export_critical_issues_excel', club_id=club.id) }}" class="export-form">
                        <div class="row g-2">
                            <div class="col-7">
                                <select class="form-select form-select-sm" name="specific_month" required>
                                    <option value="">اختر الشهر</option>
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                            </div>
                            <div class="col-5">
                                <select class="form-select form-select-sm" name="year" required>
                                    <option value="">العام</option>
                                    {% set current_year = 2024 %}
                                    {% for year in range(2020, 2030) %}
                                        <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="mt-2">
                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-download me-1"></i> تصدير
                            </button>
                        </div>
                    </form>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">تصدير حسب القسم</h6></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id, department='فنيون التكييف - A/C Technician') }}">
                    <i class="fas fa-snowflake me-1"></i> فنيون التكييف
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id, department='النجارين - Carpenter') }}">
                    <i class="fas fa-hammer me-1"></i> النجارين
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id, department='فنيون مدنيون - Civil Tech') }}">
                    <i class="fas fa-hard-hat me-1"></i> فنيون مدنيون
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id, department='كهربائيين - Electrician') }}">
                    <i class="fas fa-bolt me-1"></i> كهربائيين
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('export_critical_issues_excel', club_id=club.id, department='فنيون السباكة - Plumber') }}">
                    <i class="fas fa-wrench me-1"></i> فنيون السباكة
                </a></li>
            </ul>
        </div>
        <a href="{{ url_for('new_critical_issue') }}?club_id={{ club.id }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة عطل جديد
        </a>
        <a href="{{ url_for('critical_issues_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<!-- معلومات النادي والإحصائيات -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    {{ club.name }}
                </h5>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-list-alt me-1"></i>
                        إجمالي: {{ stats.total }}
                    </span>
                    {% if stats.pending > 0 %}
                    <span class="badge bg-warning text-dark">
                        <i class="fas fa-clock me-1"></i>
                        معلقة: {{ stats.pending }}
                    </span>
                    {% endif %}
                    {% if stats.overdue > 0 %}
                    <span class="badge bg-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        متأخرة: {{ stats.overdue }}
                    </span>
                    {% endif %}
                    {% if stats.completed > 0 %}
                    <span class="badge bg-success">
                        <i class="fas fa-check-circle me-1"></i>
                        مكتملة: {{ stats.completed }}
                    </span>
                    {% endif %}
                    {% if stats.closed > 0 %}
                    <span class="badge bg-secondary">
                        <i class="fas fa-times-circle me-1"></i>
                        مغلقة: {{ stats.closed }}
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- بحث وتصفية -->
<div class="card search-card mb-4">
    <div class="card-header py-2">
        <h6 class="mb-0"><i class="fas fa-search me-2"></i> البحث والتصفية</h6>
    </div>
    <div class="card-body py-3">
        <form method="get" action="{{ url_for('critical_issues_club_detail', club_id=club.id) }}">
            <div class="row g-3 align-items-end">
                <div class="col-md-2">
                    <label for="search" class="form-label small">رقم الطلب</label>
                    <input type="text" class="form-control form-control-sm" id="search" name="search" placeholder="رقم الطلب..." value="{{ search_query }}">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label small">الحالة</label>
                    <select class="form-select form-select-sm" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        {% for status in status_options %}
                            <option value="{{ status }}" {% if status == status_filter %}selected{% endif %}>{{ status }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="department" class="form-label small">القسم المختص</label>
                    <select class="form-select form-select-sm" id="department" name="department">
                        <option value="">جميع الأقسام</option>
                        {% for department in department_options %}
                            <option value="{{ department }}" {% if department == department_filter %}selected{% endif %}>{{ department }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-search btn-sm w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الأعطال -->
{% if issues %}
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i> قائمة الأعطال الحرجة</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="12%">رقم الطلب</th>
                        <th width="12%">تاريخ الإنشاء</th>
                        <th width="12%">تاريخ الاستحقاق</th>
                        <th width="12%">الحالة</th>
                        <th width="20%">القسم المختص</th>
                        <th class="text-center" width="17%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issues %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td class="fw-bold">{{ issue.ticket_number }}</td>
                        <td>{{ issue.creation_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if issue.due_date < today and issue.status != 'تمت الصيانة' and issue.status != 'اغلاق الطلب بدون صيانة' %}
                                <span class="text-danger fw-bold">{{ issue.due_date.strftime('%Y-%m-%d') }}</span>
                                <i class="fas fa-exclamation-triangle text-danger ms-1" title="تخطى تاريخ الاستحقاق"></i>
                            {% else %}
                                {{ issue.due_date.strftime('%Y-%m-%d') }}
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.status == 'تمت الصيانة' %}
                                <span class="badge bg-success">{{ issue.status }}</span>
                            {% elif issue.status == 'معلقة' %}
                                <span class="badge bg-warning text-dark">{{ issue.status }}</span>
                            {% elif issue.status == 'تخطت تاريخ الاستحقاق' %}
                                <span class="badge bg-danger">{{ issue.status }}</span>
                            {% elif issue.status == 'اغلاق الطلب بدون صيانة' %}
                                <span class="badge bg-secondary">{{ issue.status }}</span>
                            {% else %}
                                <span class="badge bg-info">{{ issue.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.department %}
                                <span class="badge bg-primary">{{ issue.department }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('critical_issue_detail', id=issue.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_critical_issue', id=issue.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if issue.status != 'تمت الصيانة' and issue.status != 'اغلاق الطلب بدون صيانة' %}
                                <a href="{{ url_for('close_critical_issue', id=issue.id) }}" class="btn btn-sm btn-success" title="إغلاق الطلب">
                                    <i class="fas fa-check-circle"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('delete_critical_issue', id=issue.id) }}" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف العطل الحرج؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
    <div class="card">
        <div class="card-body">
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أعطال حرجة لهذا النادي</h5>
                <p class="text-muted">قم بإضافة عطل حرج جديد للبدء</p>
                <a href="{{ url_for('new_critical_issue') }}?club_id={{ club.id }}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus-circle me-1"></i> إضافة عطل حرج جديد
                </a>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block head %}
<style>
    /* تحسين مظهر بطاقة النادي */
    .card-header.bg-primary {
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
        border-bottom: 3px solid rgba(255, 255, 255, 0.2);
    }

    /* تحسين مظهر الشارات */
    .badge {
        font-size: 0.75rem;
        padding: 0.4em 0.6em;
        border-radius: 0.375rem;
    }

    .badge.bg-light {
        border: 1px solid #dee2e6;
    }

    /* تحسين مظهر الجداول */
    .table th {
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    /* تحسين مظهر الأزرار */
    .btn-group .btn {
        border-radius: 0.25rem;
        margin: 0 1px;
    }

    /* تحسين مظهر التواريخ المتأخرة */
    .text-danger.fw-bold {
        background: linear-gradient(45deg, #dc3545, #b02a37);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    /* تحسين مظهر الأيقونات */
    .fas.fa-exclamation-triangle {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* تحسين مظهر البطاقات */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تحسين مظهر الصفوف */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر الشارات في الرأس */
    .card-header .badge {
        font-size: 0.7rem;
        font-weight: 500;
    }

    /* تحسين المسافات */
    .gap-2 {
        gap: 0.5rem !important;
    }

    /* تحسين بطاقة البحث */
    .search-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .search-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-bottom: none;
        border-radius: 0.35rem 0.35rem 0 0;
    }

    .search-card .card-body {
        background-color: #f8f9fc;
    }

    /* تحسين الحقول */
    .form-control-sm, .form-select-sm {
        border-radius: 0.25rem;
        border: 1px solid #d1d3e2;
        transition: all 0.3s ease;
    }

    .form-control-sm:focus, .form-select-sm:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* تحسين زر البحث */
    .btn-search {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 0.25rem;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-search:hover {
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.3);
    }

    /* تحسين التسميات */
    .form-label.small {
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    /* تحسين المسافات للبحث */
    .row.g-3 {
        --bs-gutter-x: 1rem;
        --bs-gutter-y: 0.75rem;
    }

    /* تحسين التصميم المتجاوب */
    @media (max-width: 768px) {
        .col-md-2, .col-md-3, .col-md-5 {
            margin-bottom: 0.5rem;
        }

        .search-card .card-body {
            padding: 1rem;
        }

        .form-label.small {
            font-size: 0.8rem;
        }

        .btn-search {
            margin-top: 0.5rem;
        }
    }

    /* تحسين الأيقونات */
    .fas.fa-search {
        font-size: 0.875rem;
    }

    /* تأثير focus للحقول */
    .focused {
        transform: scale(1.02);
        transition: transform 0.2s ease;
    }

    /* تحسين placeholder */
    .form-control-sm::placeholder {
        color: #a0a6b8;
        font-style: italic;
    }

    /* تحسين تأثير hover للحقول */
    .form-control-sm:hover, .form-select-sm:hover {
        border-color: #667eea;
        transition: border-color 0.3s ease;
    }
</style>
{% endblock %}



{% block scripts %}
<script>
    $(document).ready(function() {
        // تغيير الحالة أو القسم يؤدي إلى إرسال النموذج تلقائياً
        $('#status, #department').change(function() {
            // إضافة تأثير تحميل
            const $btn = $('.btn-search');
            const originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري البحث...');
            $btn.prop('disabled', true);

            $(this).closest('form').submit();
        });

        // تحسين تفاعل حقل البحث
        $('#search').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                $(this).closest('form').submit();
            }
        });

        // إضافة تأثير focus للحقول
        $('.form-control-sm, .form-select-sm').on('focus', function() {
            $(this).parent().addClass('focused');
        }).on('blur', function() {
            $(this).parent().removeClass('focused');
        });

        // إضافة tooltip للشارات
        $('[title]').tooltip();

        // تحسين زر البحث
        $('.btn-search').on('click', function() {
            const $this = $(this);
            const originalText = $this.html();
            $this.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري البحث...');
            $this.prop('disabled', true);
        });
    });
</script>
{% endblock %}
