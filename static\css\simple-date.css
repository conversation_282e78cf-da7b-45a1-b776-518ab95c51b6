/* تنسيق حقول التاريخ */
input[type="text"].english-number {
    font-family: 'Segoe UI', Arial, sans-serif !important;
    direction: ltr !important;
    height: 50px;
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: #fff;
    position: relative;
    cursor: pointer;
    width: 100%;
    unicode-bidi: plaintext;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    width: 30px;
    height: 30px;
    cursor: pointer;
    opacity: 0.8;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

input[type="date"]:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تن<PERSON>يق حقل التاريخ المعطل */
input[type="date"]:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
    cursor: not-allowed;
}

/* تنسيق حقل التاريخ النشط */
.active-field input[type="date"] {
    border-color: #28a745;
    border-width: 2px;
}

.active-field label {
    color: #28a745;
    font-weight: bold;
}

/* إضافة أيقونة لحقل التاريخ النشط */
.active-field label::after {
    content: " ✓";
    color: #28a745;
}

/* تنسيق حاوية حقل التاريخ */
.date-input-container {
    position: relative;
}

/* تنسيق الأيقونة في حقل الإدخال */
.date-input-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0d6efd;
    pointer-events: none;
}

/* تنسيق للتأكد من ظهور التقويم */
input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-clear-button {
    display: none;
}

/* تنسيق لإظهار التقويم عند النقر */
input[type="date"]:focus::-webkit-calendar-picker-indicator {
    opacity: 1;
}

/* تنسيق لتحسين مظهر التقويم */
::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
    font-size: 20px;
}

::-webkit-datetime-edit-text {
    color: #495057;
    padding: 0 0.2em;
}

::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-year-field {
    color: #495057;
}

::-webkit-datetime-edit-month-field:focus,
::-webkit-datetime-edit-day-field:focus,
::-webkit-datetime-edit-year-field:focus {
    background-color: #e6f0ff;
    color: #0d6efd;
    outline: none;
}

/* تنسيق لإظهار الأرقام باللغة الإنجليزية */
input[type="date"]::-webkit-datetime-edit {
    font-family: 'Segoe UI', Arial, sans-serif !important;
    direction: ltr !important;
    unicode-bidi: plaintext;
}

/* تنسيق حقول التاريخ المحولة إلى نص */
input[type="text"][data-is-date="true"] {
    font-family: 'Segoe UI', Arial, sans-serif !important;
    direction: ltr !important;
    text-align: center;
    font-size: 18px;
    height: 50px;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    background-color: #fff;
    cursor: pointer;
    width: 100%;
}

/* تنسيق لتحسين عرض placeholder */
input[type="date"]::placeholder {
    direction: ltr !important;
    text-align: center;
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

/* تنسيق لمجموعة الإدخال */
.input-group-text {
    background-color: #f8f9fa;
    cursor: pointer;
}

.input-group-text:hover {
    background-color: #e9ecef;
}

/* تنسيق لأيقونة التقويم */
.input-group-text .fas {
    color: #0d6efd;
}

/* تنسيقات Flatpickr */
.flatpickr-calendar {
    font-family: 'Segoe UI', Arial, sans-serif !important;
    font-size: 14px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 10px;
    width: 300px;
    z-index: 9999 !important;
}

.flatpickr-month {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 5px;
    color: #333;
}

.flatpickr-current-month {
    font-weight: bold;
    padding: 5px 0;
}

.flatpickr-weekday {
    color: #666;
    font-weight: normal;
    text-align: center;
    background: transparent;
}

.flatpickr-day {
    text-align: center;
    padding: 7px;
    border-radius: 4px;
    transition: all 0.2s;
    margin: 2px;
    border: none;
}

.flatpickr-day.selected {
    background: #0d6efd;
    color: #fff;
    border: none;
}

.flatpickr-day.today {
    background: #e6f2ff;
    color: #0d6efd;
    border: none;
}

.flatpickr-day:hover {
    background: #e9ecef;
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    color: #ccc;
    background: transparent;
    cursor: not-allowed;
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-current-month input.cur-year {
    font-weight: bold;
    color: #333;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover,
.flatpickr-current-month input.cur-year:hover {
    background: #e9ecef;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    padding: 5px;
    fill: #666;
}

.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
    fill: #333;
}

/* تأكيد أن التقويم يظهر فوق العناصر الأخرى */
.flatpickr-calendar {
    z-index: 9999 !important;
}

/* تنسيق إضافي لحقول التاريخ */
.datepicker {
    cursor: pointer !important;
}

/* تنسيق لإظهار أيقونة التقويم بشكل واضح */
.input-group-text {
    cursor: pointer !important;
    background-color: #f0f0f0 !important;
}

.input-group-text:hover {
    background-color: #e0e0e0 !important;
}

.input-group-text .fas {
    font-size: 18px !important;
}
