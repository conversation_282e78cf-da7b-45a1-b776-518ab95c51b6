/* تنسيق منتقي التاريخ المخصص */
.custom-datepicker-input {
    font-family: 'Segoe UI', Arial, sans-serif !important;
    direction: ltr !important;
    height: 38px;
    font-size: 14px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: #fff;
    position: relative;
    cursor: pointer;
}

.custom-datepicker-input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تنسيق منتقي التاريخ Flatpickr */
.flatpickr-calendar {
    display: block !important;
    z-index: 9999 !important;
    direction: rtl !important;
    font-family: 'Cairo', sans-serif !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    border: none;
    padding: 10px;
}

.flatpickr-calendar.open {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
}

.flatpickr-months {
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
    padding: 10px 0;
}

.flatpickr-month {
    height: 40px;
}

.flatpickr-current-month {
    font-size: 15px;
    font-weight: 600;
    color: #0d6efd;
}

.flatpickr-monthDropdown-months {
    font-weight: 600;
    color: #0d6efd;
    font-size: 15px;
}

.flatpickr-weekday {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    height: 36px;
    line-height: 36px;
}

.flatpickr-day {
    border-radius: 50%;
    height: 36px;
    line-height: 36px;
    margin: 2px;
    font-weight: 500;
    color: #333;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover {
    background: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.flatpickr-day.today {
    border-color: #0d6efd;
    color: #0d6efd;
    font-weight: 700;
}

.flatpickr-day.today:hover,
.flatpickr-day.today:focus {
    background: #e6f0ff;
    border-color: #0d6efd;
    color: #0d6efd;
}

.flatpickr-day:hover {
    background: #f0f0f0;
}

.flatpickr-prev-month,
.flatpickr-next-month {
    padding: 5px;
    fill: #0d6efd !important;
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
    background: #e6f0ff;
    border-radius: 50%;
}

/* تنسيق أيام الشهر السابق والتالي */
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay {
    color: #adb5bd;
}

/* تنسيق اليوم المعطل */
.flatpickr-day.disabled,
.flatpickr-day.disabled:hover {
    color: #dee2e6;
    cursor: not-allowed;
    background: transparent;
}

/* تنسيق الأيام خارج الشهر الحالي */
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    color: #dee2e6;
}

/* تنسيق الأيام المحددة */
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange {
    box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
}

/* تنسيق الأشهر */
.flatpickr-months .flatpickr-month {
    background-color: transparent;
}

/* تنسيق أسماء الأيام */
.flatpickr-weekdays {
    background-color: transparent;
    margin-top: 5px;
}

/* تنسيق أيام الأسبوع */
.flatpickr-weekday {
    background-color: transparent;
}

/* تنسيق الأيام */
.dayContainer {
    padding: 5px 0;
}

/* تنسيق الأيام في الأسبوع */
.flatpickr-days {
    border-radius: 0 0 8px 8px;
}

/* تنسيق الشهر الحالي */
.flatpickr-current-month {
    padding: 0;
}

/* تنسيق اختيار الشهر */
.flatpickr-monthDropdown-months {
    background-color: transparent;
    border: none;
    cursor: pointer;
}

/* تنسيق اختيار السنة */
.numInputWrapper {
    margin-left: 5px;
}

.numInputWrapper input {
    border: none;
    background-color: transparent;
    color: #0d6efd;
    font-weight: 600;
    font-size: 18px;
    width: 60px;
    text-align: center;
}

.numInputWrapper span {
    display: none;
}

/* تنسيق الأيقونة في حقل الإدخال */
.date-input-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0d6efd;
    pointer-events: none;
}

/* تنسيق حاوية حقل التاريخ */
.date-input-container {
    position: relative;
}

/* تنسيق الأيام المختلفة */
.flatpickr-day.today.selected {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

/* تنسيق الأيام المعطلة */
.flatpickr-day.disabled {
    color: #dee2e6;
    cursor: not-allowed;
}

/* تنسيق الأيام المختارة */
.flatpickr-day.selected {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

/* تنسيق الأيام عند التحويم */
.flatpickr-day:hover:not(.disabled):not(.selected) {
    background-color: #e6f0ff;
    border-color: #e6f0ff;
}

/* تنسيق الأيام في النطاق */
.flatpickr-day.inRange {
    background-color: #e6f0ff;
    border-color: #e6f0ff;
    box-shadow: -5px 0 0 #e6f0ff, 5px 0 0 #e6f0ff;
}

/* تنسيق الأيام في بداية ونهاية النطاق */
.flatpickr-day.startRange, .flatpickr-day.endRange {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

/* تنسيق الأيام في بداية النطاق */
.flatpickr-day.startRange {
    border-radius: 50% 0 0 50%;
}

/* تنسيق الأيام في نهاية النطاق */
.flatpickr-day.endRange {
    border-radius: 0 50% 50% 0;
}
