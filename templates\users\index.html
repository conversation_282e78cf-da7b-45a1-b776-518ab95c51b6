{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">المستخدمين</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('new_user') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة مستخدم جديد
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <form method="get" action="{{ url_for('users') }}">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="بحث..." name="search" value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" width="7%" >الرقم الوظيفي</th>
                                <th class="text-center" width="12%">الاسم</th>
                                <th class="text-center" width="7%">رقم الهاتف</th>
                                <th class="text-center" width="8%">الدور</th>
                                <th class="text-center" width="8%">الأندية</th>
                                <th class="text-center" width="8%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td class="py-2 text-center">{{ user.username }}</td>
                                <td class="py-2 text-center">{{ user.name }}</td>
                                <td class="py-2 text-center">{{ user.phone }}</td>
                                <td class="py-2 text-center">
                                    {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">مسؤول النظام</span>
                                    {% elif user.role == 'manager' %}
                                    <span class="badge bg-warning text-dark">مدير</span>
                                    {% elif user.role == 'supervisor' %}
                                    <span class="badge bg-info">مشرف</span>
                                    {% else %}
                                    <span class="badge bg-success">مستخدم عادي</span>
                                    {% endif %}
                                </td>
                                <td class="py-2 text-center">
                                    <span class="badge bg-primary">{{ user.clubs|length }}</span>
                                </td>
                                <td class="py-2">
                                    <a href="{{ url_for('user_detail', id=user.id) }}" class="btn btn-sm btn-info py-1 px-2">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('edit_user', id=user.id) }}" class="btn btn-sm btn-warning py-1 px-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ url_for('delete_user', id=user.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف المستخدم {{ user.name }}؟');">
                                        <button type="submit" class="btn btn-sm btn-danger py-1 px-2">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
