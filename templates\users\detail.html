{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3 fw-bold">تفاصيل المستخدم</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('users') }}" class="btn btn-secondary btn-lg me-1">
            <i class="fas fa-arrow-right me-1"></i> العودة
        </a>
        <a href="{{ url_for('edit_user', id=user.id) }}" class="btn btn-warning btn-lg me-1">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="{{ url_for('user_permissions', id=user.id) }}" class="btn btn-primary btn-lg">
            <i class="fas fa-shield-alt me-1"></i> الصلاحيات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white py-3">
                <h4 class="card-title mb-0 fw-bold"><i class="fas fa-user me-2"></i>{{ user.name }}</h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                <i class="fas fa-id-badge"></i>
                            </div>
                            <div>
                                <small class="text-muted d-block">الرقم الوظيفي</small>
                                <strong class="fs-5">{{ user.username }}</strong>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <small class="text-muted d-block">رقم الهاتف</small>
                                <strong class="fs-5">{{ user.phone }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                <i class="fas fa-user-tag"></i>
                            </div>
                            <div>
                                <small class="text-muted d-block">الدور</small>
                                {% if user.role == 'admin' %}
                                <span class="badge bg-danger fs-6">مسؤول النظام</span>
                                {% elif user.role == 'manager' %}
                                <span class="badge bg-warning text-dark fs-6">مدير</span>
                                {% elif user.role == 'supervisor' %}
                                <span class="badge bg-info fs-6">مشرف</span>
                                {% else %}
                                <span class="badge bg-success fs-6">مستخدم عادي</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                <i class="fas fa-building"></i>
                            </div>
                            <div>
                                <small class="text-muted d-block" >الأندية</small>
                                <div class="clubs-container">
                                    {% for club in user.clubs %}
                                    <span class="badge club-badge me-1 fs-6">{{ club.name }}</span>
                                    {% else %}
                                    <span class="text-muted">لا توجد أندية مرتبطة</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
        border-bottom: none;
    }

    .icon-box {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .icon-box:hover {
        transform: scale(1.1);
    }

    .badge {
        padding: 0.5em 1em;
        font-weight: 500;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: translateY(-2px);
    }

    .user-info-item {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .user-info-item:hover {
        background-color: #e9ecef;
        transform: translateX(-5px);
    }

    .user-info-label {
        color: #6c757d;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .user-info-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #212529;
    }

    .clubs-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .club-badge {
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .club-badge:hover {
        transform: translateY(-2px);
        border-color: rgba(255, 255, 255, 0.5);
    }
</style>
{% endblock %}
