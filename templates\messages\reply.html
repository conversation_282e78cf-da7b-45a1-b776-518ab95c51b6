{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-reply me-2"></i>
                    الرد على الرسالة
                </h2>
                <div>
                    <a href="{{ url_for('view_message', id=original_message.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للرسالة
                    </a>
                    <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-inbox"></i>
                        صندوق الوارد
                    </a>
                </div>
            </div>

            <!-- عرض الرسالة الأصلية -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        الرسالة الأصلية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>الموضوع:</strong> {{ original_message.subject }}
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>المرسل:</strong> {{ original_message.sender.name }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>التاريخ:</strong> {{ original_message.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </div>
                        <div class="col-md-6 text-end">
                            {% if original_message.priority == 'high' %}
                                <span class="badge bg-danger">أولوية عالية</span>
                            {% elif original_message.priority == 'normal' %}
                                <span class="badge bg-success">أولوية عادية</span>
                            {% elif original_message.priority == 'low' %}
                                <span class="badge bg-secondary">أولوية منخفضة</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="border rounded p-3 bg-light">
                        {{ original_message.content|nl2br|safe }}
                    </div>
                    
                    <!-- مرفقات الرسالة الأصلية -->
                    {% if original_message.attachments %}
                        <div class="mt-3">
                            <h6><i class="fas fa-paperclip me-2"></i>المرفقات:</h6>
                            <div class="row">
                                {% for attachment in original_message.attachments %}
                                    <div class="col-md-6 mb-2">
                                        <div class="border rounded p-2 d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="fas fa-file me-2"></i>
                                                <small>{{ attachment.original_filename }}</small>
                                            </div>
                                            <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- نموذج الرد -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        كتابة الرد
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <!-- موضوع الرد (للعرض فقط) -->
                        <div class="mb-3">
                            <label class="form-label">موضوع الرد</label>
                            <input type="text" class="form-control" value="رد: {{ original_message.subject }}" readonly>
                        </div>

                        <!-- محتوى الرد -->
                        <div class="mb-3">
                            <label for="content" class="form-label">محتوى الرد <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" required 
                                      placeholder="اكتب ردك هنا..."></textarea>
                        </div>

                        <!-- المرفقات -->
                        <div class="mb-3">
                            <label for="attachments" class="form-label">المرفقات</label>
                            <input type="file" class="form-control" id="attachments" name="attachments" multiple 
                                   accept=".txt,.pdf,.png,.jpg,.jpeg,.gif,.doc,.docx,.xls,.xlsx">
                            <div class="form-text">
                                يمكنك إرفاق ملفات من الأنواع التالية: TXT, PDF, PNG, JPG, JPEG, GIF, DOC, DOCX, XLS, XLSX
                            </div>
                        </div>

                        <!-- معاينة المرفقات المحددة -->
                        <div id="attachments-preview" class="mb-3" style="display: none;">
                            <h6>المرفقات المحددة:</h6>
                            <div id="attachments-list"></div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الرد
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearReply()">
                                    <i class="fas fa-eraser"></i>
                                    مسح المحتوى
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('view_message', id=original_message.id) }}" class="btn btn-outline-danger">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نصائح للرد -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="text-primary">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح للرد الفعال:
                    </h6>
                    <ul class="mb-0">
                        <li>كن واضحاً ومباشراً في ردك</li>
                        <li>أجب على جميع النقاط المطروحة في الرسالة الأصلية</li>
                        <li>استخدم لغة مهذبة ومهنية</li>
                        <li>تأكد من مراجعة ردك قبل الإرسال</li>
                        <li>أرفق أي مستندات ضرورية لتوضيح ردك</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearReply() {
    if (confirm('هل أنت متأكد من مسح محتوى الرد؟')) {
        document.getElementById('content').value = '';
        document.getElementById('attachments').value = '';
        hideAttachmentsPreview();
    }
}

// معاينة المرفقات المحددة
document.getElementById('attachments').addEventListener('change', function(e) {
    const files = e.target.files;
    const preview = document.getElementById('attachments-preview');
    const list = document.getElementById('attachments-list');
    
    if (files.length > 0) {
        list.innerHTML = '';
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileDiv = document.createElement('div');
            fileDiv.className = 'border rounded p-2 mb-2 d-flex justify-content-between align-items-center';
            fileDiv.innerHTML = `
                <div>
                    <i class="fas fa-file me-2"></i>
                    <strong>${file.name}</strong>
                    <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                </div>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeFile(${i})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            list.appendChild(fileDiv);
        }
        preview.style.display = 'block';
    } else {
        hideAttachmentsPreview();
    }
});

function hideAttachmentsPreview() {
    document.getElementById('attachments-preview').style.display = 'none';
    document.getElementById('attachments-list').innerHTML = '';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function removeFile(index) {
    const input = document.getElementById('attachments');
    const dt = new DataTransfer();
    const files = input.files;
    
    for (let i = 0; i < files.length; i++) {
        if (i !== index) {
            dt.items.add(files[i]);
        }
    }
    
    input.files = dt.files;
    input.dispatchEvent(new Event('change'));
}

// تحسين تجربة المستخدم - التركيز على محتوى الرد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('content').focus();
});

// حفظ المسودة تلقائياً كل دقيقة
let draftTimer;
function saveDraft() {
    const content = document.getElementById('content').value;
    if (content.trim()) {
        localStorage.setItem('reply_draft_{{ original_message.id }}', content);
    }
}

function loadDraft() {
    const draft = localStorage.getItem('reply_draft_{{ original_message.id }}');
    if (draft) {
        document.getElementById('content').value = draft;
        // إظهار رسالة للمستخدم
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            تم استرداد مسودة محفوظة من جلسة سابقة.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.card-body form').insertBefore(alert, document.querySelector('.card-body form').firstChild);
    }
}

// تحميل المسودة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadDraft);

// حفظ المسودة عند الكتابة
document.getElementById('content').addEventListener('input', function() {
    clearTimeout(draftTimer);
    draftTimer = setTimeout(saveDraft, 2000); // حفظ بعد ثانيتين من التوقف عن الكتابة
});

// مسح المسودة عند الإرسال
document.querySelector('form').addEventListener('submit', function() {
    localStorage.removeItem('reply_draft_{{ original_message.id }}');
});
</script>
{% endblock %}
