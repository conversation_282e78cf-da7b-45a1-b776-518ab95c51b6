{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-3">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="text-primary mb-0"><i class="fas fa-edit me-2"></i> تعديل المبيعات اليومية</h5>
        <a href="{{ url_for('sales_target_detail', id=daily_sale.target_id) }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى التفاصيل
        </a>
    </div>

    <!-- نموذج تعديل المبيعات -->
    <div class="card shadow">
        <div class="card-header bg-white py-2">
            <h6 class="mb-0 text-primary"><i class="fas fa-edit me-2"></i> تعديل مبيعات {{ daily_sale.sale_date.strftime('%Y-%m-%d') }}</h6>
        </div>
        <div class="card-body p-3">
            <form method="post" action="{{ url_for('edit_daily_sales', id=daily_sale.id) }}">
                <!-- معلومات أساسية -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <label class="form-label small">النادي</label>
                            <input type="text" class="form-control form-control-sm" value="{{ daily_sale.target.club.name }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <label class="form-label small">التاريخ</label>
                            <input type="date" class="form-control form-control-sm" id="sale_date" name="sale_date" value="{{ daily_sale.sale_date.strftime('%Y-%m-%d') }}" style="text-align: center; direction: ltr;" required>
                        </div>
                    </div>
                </div>

                <!-- حقول المبيعات المفصلة -->
                <div class="card border-info mb-3">
                    <div class="card-header bg-info text-white py-2">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i> تفاصيل المبيعات</h6>
                    </div>
                    <div class="card-body p-3">
                        <div class="row">
                            <!-- مبيعات الاشتراكات -->
                            <div class="col-md-6 mb-2">
                                <label for="subscription_sales_club" class="form-label small">مبيعات الاشتراكات (النادي)</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                    <input type="text" class="form-control numeric-input" id="subscription_sales_club" name="subscription_sales_club" value="{% if daily_sale.subscription_sales_club and daily_sale.subscription_sales_club > 0 %}{{ '{:g}'.format(daily_sale.subscription_sales_club) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="subscription_sales_online" class="form-label small">مبيعات الاشتراكات (أونلاين)</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                    <input type="text" class="form-control numeric-input" id="subscription_sales_online" name="subscription_sales_online" value="{% if daily_sale.subscription_sales_online and daily_sale.subscription_sales_online > 0 %}{{ '{:g}'.format(daily_sale.subscription_sales_online) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            <!-- مبيعات التدريب الشخصي -->
                            <div class="col-md-6 mb-2">
                                <label for="personal_training_sales" class="form-label small">مبيعات التدريب الشخصي</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-dumbbell"></i></span>
                                    <input type="text" class="form-control numeric-input" id="personal_training_sales" name="personal_training_sales" value="{% if daily_sale.personal_training_sales and daily_sale.personal_training_sales > 0 %}{{ '{:g}'.format(daily_sale.personal_training_sales) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            <!-- مبيعات الخدمات -->
                            <div class="col-md-6 mb-2">
                                <label for="services_sales_club" class="form-label small">مبيعات الخدمات (النادي)</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-cogs"></i></span>
                                    <input type="text" class="form-control numeric-input" id="services_sales_club" name="services_sales_club" value="{% if daily_sale.services_sales_club and daily_sale.services_sales_club > 0 %}{{ '{:g}'.format(daily_sale.services_sales_club) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <small class="text-muted">إيقاف - تنازل</small>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="services_sales_online" class="form-label small">مبيعات الخدمات (أونلاين)</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-laptop"></i></span>
                                    <input type="text" class="form-control numeric-input" id="services_sales_online" name="services_sales_online" value="{% if daily_sale.services_sales_online and daily_sale.services_sales_online > 0 %}{{ '{:g}'.format(daily_sale.services_sales_online) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <small class="text-muted">إيقاف - تنازل</small>
                            </div>
                            <!-- مبيعات انبودي والاكسسوار -->
                            <div class="col-md-6 mb-2">
                                <label for="anybody_sales" class="form-label small">مبيعات انبودي</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-heart"></i></span>
                                    <input type="text" class="form-control numeric-input" id="anybody_sales" name="anybody_sales" value="{% if daily_sale.anybody_sales and daily_sale.anybody_sales > 0 %}{{ '{:g}'.format(daily_sale.anybody_sales) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="accessories_sales" class="form-label small">مبيعات الاكسسوار</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-shopping-bag"></i></span>
                                    <input type="text" class="form-control numeric-input" id="accessories_sales" name="accessories_sales" value="{% if daily_sale.accessories_sales and daily_sale.accessories_sales > 0 %}{{ '{:g}'.format(daily_sale.accessories_sales) }}{% endif %}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- حقل الإجمالي -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <label for="total_amount" class="form-label small fw-bold">إجمالي المبيعات</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text bg-success text-white"><i class="fas fa-calculator"></i></span>
                                <input type="text" class="form-control fw-bold bg-light" id="total_amount" name="total_amount" value="{% if daily_sale.amount and daily_sale.amount > 0 %}{{ '{:g}'.format(daily_sale.amount) }}{% endif %}" readonly>
                                <span class="input-group-text bg-success text-white">ريال</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-sm px-4">
                        <i class="fas fa-save me-1"></i> حفظ التعديلات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
    /* تنسيق حقل التاريخ */
    #sale_date {
        font-family: Arial, sans-serif !important;
        direction: ltr !important;
    }

    /* إزالة أسهم حقل الرقم لجميع المتصفحات */
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button,
    input[type="text"].numeric-input::-webkit-inner-spin-button,
    input[type="text"].numeric-input::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    input[type="number"],
    input[type="text"].numeric-input {
        -moz-appearance: textfield;
        appearance: textfield;
    }

    /* تحسين شكل الصفحة */
    .form-label.small {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .card-header h6 {
        font-size: 0.95rem;
    }

    .input-group-sm .input-group-text {
        font-size: 0.8rem;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // قائمة حقول المبيعات
        const salesFields = [
            'subscription_sales_club',
            'subscription_sales_online',
            'personal_training_sales',
            'services_sales_club',
            'services_sales_online',
            'anybody_sales',
            'accessories_sales'
        ];

        // دالة حساب الإجمالي
        function calculateTotal() {
            let total = 0;
            let hasValues = false;

            salesFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field && field.value.trim() !== '') {
                    const value = parseFloat(field.value.replace(/[^0-9.]/g, '')) || 0;
                    total += value;
                    if (value > 0) hasValues = true;
                }
            });

            // تحديث حقل الإجمالي
            const totalField = document.getElementById('total_amount');
            if (totalField) {
                if (hasValues || total > 0) {
                    // عرض الرقم كرقم صحيح بدون فاصلة عشرية إذا كان رقماً صحيحاً
                    if (total % 1 === 0) {
                        totalField.value = total.toString();
                    } else {
                        totalField.value = total.toLocaleString('en-US', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 2
                        });
                    }
                } else {
                    totalField.value = '';
                }
            }
        }

        // إضافة معالجات الأحداث لجميع حقول المبيعات
        salesFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                // تنظيف القيمة عند الإدخال
                field.addEventListener('input', function() {
                    // السماح بالأرقام والنقطة العشرية فقط
                    this.value = this.value.replace(/[^0-9.]/g, '');

                    // منع أكثر من نقطة عشرية واحدة
                    const parts = this.value.split('.');
                    if (parts.length > 2) {
                        this.value = parts[0] + '.' + parts.slice(1).join('');
                    }

                    // حساب الإجمالي
                    calculateTotal();
                });

                // حساب الإجمالي عند فقدان التركيز
                field.addEventListener('blur', function() {
                    calculateTotal();
                });
            }
        });

        // تعيين لغة حقل التاريخ للإنجليزية
        document.getElementById('sale_date').lang = 'en';

        // حساب الإجمالي عند تحميل الصفحة
        calculateTotal();
    });
</script>
{% endblock %}
