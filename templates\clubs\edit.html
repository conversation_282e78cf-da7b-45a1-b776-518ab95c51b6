{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>تعديل بيانات النادي</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('clubs') }}">النوادي</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('club_detail', id=club.id) }}">{{ club.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">تعديل</li>
            </ol>
        </nav>
    </div>
    <a href="{{ url_for('club_detail', id=club.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل النادي
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-edit me-2"></i> تعديل بيانات النادي</h4>
                <span class="badge bg-dark">رقم {{ club.id }}</span>
            </div>
            <div class="card-body">
                <form action="" method="post" novalidate>
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم النادي <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <input type="text" class="form-control" id="name" name="name" value="{{ club.name }}" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="manager_name" class="form-label">اسم المدير</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
                                <input type="text" class="form-control" id="manager_name" name="manager_name" value="{{ club.manager_name or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ club.phone or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- قسم كثافة الموظفين -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-users me-2"></i> كثافة الموظفين للنادي</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="target_customer_service_count" class="form-label">عدد الموظفين (خدمة العملاء)</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
                                            <input type="text" class="form-control" id="target_customer_service_count" name="target_customer_service_count" value="{{ club.target_customer_service_count or 0 }}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="target_trainer_count" class="form-label">عدد المدربين</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-dumbbell"></i></span>
                                            <input type="text" class="form-control" id="target_trainer_count" name="target_trainer_count" value="{{ club.target_trainer_count or 0 }}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="target_worker_count" class="form-label">عدد العمال</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-hard-hat"></i></span>
                                            <input type="text" class="form-control" id="target_worker_count" name="target_worker_count" value="{{ club.target_worker_count or 0 }}" placeholder="0" pattern="[0-9]*" inputmode="numeric">
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">حدد العدد المطلوب لكل نوع من الموظفين في هذا النادي</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">مرافق النادي</label>
                        <div class="border rounded p-3">
                            <div class="row">
                                {% for facility in facilities %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities" value="{{ facility.id }}" id="facility{{ facility.id }}"
                                               {% if facility in club.facilities %}checked{% endif %}>
                                        <label class="form-check-label" for="facility{{ facility.id }}">
                                            {{ facility.name }}
                                            {% if not facility.is_active %}
                                            <span class="badge bg-danger ms-1">معطل</span>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <small class="text-muted">اختر المرافق المتوفرة في هذا النادي</small>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        الحقول المعلمة بعلامة <span class="text-danger">*</span> إلزامية
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('club_detail', id=club.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> معلومات النادي</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="display-4 text-primary">
                        <i class="fas fa-building"></i>
                    </div>
                    <h4>{{ club.name }}</h4>
                </div>

                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>عدد الموظفين:</span>
                        <span class="badge bg-primary rounded-pill">{{ club.employees|length }}</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i> نصائح</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكد من صحة البيانات قبل الحفظ
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
