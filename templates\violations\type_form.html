{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">
            {% if action == 'add' %}
            إضافة نوع مخالفة جديد
            {% else %}
            تعديل نوع المخالفة
            {% endif %}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_clubs_list') }}">سجل المخالفات</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violation_types_list') }}">أنواع المخالفات</a></li>
                <li class="breadcrumb-item active" aria-current="page">
                    {% if action == 'add' %}
                    إضافة نوع مخالفة جديد
                    {% else %}
                    تعديل نوع المخالفة
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
    <a href="{{ url_for('violation_types_list') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
    </a>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    {% if action == 'add' %}
                    <i class="fas fa-plus-circle me-2"></i> إضافة نوع مخالفة جديد
                    {% else %}
                    <i class="fas fa-edit me-2"></i> تعديل نوع المخالفة
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="post" action="{% if action == 'add' %}{{ url_for('add_violation_type') }}{% else %}{{ url_for('edit_violation_type', id=violation_type.id) }}{% endif %}">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم نوع المخالفة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{% if violation_type %}{{ violation_type.name }}{% endif %}" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المخالفة</label>
                        <textarea class="form-control" id="description" name="description" rows="4">{% if violation_type %}{{ violation_type.description }}{% endif %}</textarea>
                    </div>
                    <div class="mb-4 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if not violation_type or violation_type.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">نشط</label>
                    </div>
                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('violation_types_list') }}" class="btn btn-secondary me-2">إلغاء</a>
                        <button type="submit" class="btn btn-primary">
                            {% if action == 'add' %}
                            <i class="fas fa-plus-circle me-1"></i> إضافة
                            {% else %}
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
