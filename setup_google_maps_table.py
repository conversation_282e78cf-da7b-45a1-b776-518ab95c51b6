import sqlite3
import os
from datetime import date, timedelta

# الاتصال بقاعدة البيانات
db_path = 'app.db'
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

try:
    # إنشاء الجدول
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS google_maps_rating (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            club_id INTEGER NOT NULL,
            date DATE NOT NULL,
            rating_count INTEGER NOT NULL DEFAULT 0,
            rating_average REAL NOT NULL DEFAULT 0.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (club_id) REFERENCES club (id)
        )
    ''')
    
    print("تم إنشاء جدول google_maps_rating بنجاح!")
    
    # إضافة بعض البيانات التجريبية
    sample_data = [
        (1, (date.today() - timedelta(days=30)).isoformat(), 145, 4.2),
        (1, (date.today() - timedelta(days=15)).isoformat(), 152, 4.3),
        (1, date.today().isoformat(), 158, 4.4),
        (2, (date.today() - timedelta(days=20)).isoformat(), 89, 3.8),
        (2, (date.today() - timedelta(days=5)).isoformat(), 95, 3.9),
        (3, (date.today() - timedelta(days=10)).isoformat(), 203, 4.6),
    ]
    
    # التحقق من وجود البيانات أولاً
    cursor.execute("SELECT COUNT(*) FROM google_maps_rating")
    count = cursor.fetchone()[0]
    
    if count == 0:
        cursor.executemany('''
            INSERT INTO google_maps_rating (club_id, date, rating_count, rating_average)
            VALUES (?, ?, ?, ?)
        ''', sample_data)
        
        print(f"تم إضافة {len(sample_data)} تقييم تجريبي!")
    else:
        print(f"يوجد بالفعل {count} تقييم في الجدول")
    
    # حفظ التغييرات
    conn.commit()
    
    # عرض البيانات
    cursor.execute("SELECT * FROM google_maps_rating ORDER BY date DESC")
    ratings = cursor.fetchall()
    
    print("\nالتقييمات الموجودة:")
    for rating in ratings:
        print(f"ID: {rating[0]}, النادي: {rating[1]}, التاريخ: {rating[2]}, العدد: {rating[3]}, المتوسط: {rating[4]}")
    
except Exception as e:
    print(f"حدث خطأ: {e}")
    
finally:
    conn.close()
    print("تم إغلاق الاتصال بقاعدة البيانات")
