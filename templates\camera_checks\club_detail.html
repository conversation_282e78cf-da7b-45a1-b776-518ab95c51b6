{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">متابعة كاميرات {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('camera_checks_clubs_list') }}">سجل متابعة الكاميرات</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ club.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('new_camera_check_for_club', club_id=club.id) }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> متابعة كاميرات جديدة
        </a>
        <a href="{{ url_for('camera_checks_clubs_list') }}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<!-- معلومات النادي -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> معلومات النادي</h5>
    </div>
    <div class="card-body py-3">
        <div class="row align-items-center">
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <span class="text-muted ms-2">اسم النادي:</span>
                    <span class="fw-bold">{{ club.name }}</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <span class="text-muted ms-2">الموقع:</span>
                    <span class="fw-bold">{{ club.location }}</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <span class="text-muted ms-2">مدير النادي:</span>
                    <span class="fw-bold">{{ club.manager_name }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة متابعات الكاميرات -->
<div class="card shadow-sm">
    <div class="card-header bg-light py-3">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-0"><i class="fas fa-video me-2"></i> قائمة متابعات الكاميرات</h5>
            </div>
            <div class="col-md-4">
                <form action="{{ url_for('camera_checks_club_detail', club_id=club.id) }}" method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{{ url_for('camera_checks_club_detail', club_id=club.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if camera_checks %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th class="text-center" width="15%">تاريخ المتابعة</th>
                        <th class="text-center" width="15%">عدد المخالفات</th>
                        <th width="40%">ملاحظات</th>
                        <th class="text-center" width="25%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for check in camera_checks %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td class="text-center english-number">{{ check.check_date.strftime('%Y-%m-%d') }}</td>
                        <td class="text-center">{{ check.violations_count }}</td>
                        <td>{{ check.notes }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('camera_check_detail', id=check.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_camera_check', id=check.id) }}" class="btn btn-sm btn-warning py-1 px-2" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('new_camera_check_for_club', club_id=club.id) }}" class="btn btn-sm btn-success py-1 px-2" title="إضافة متابعة جديدة">
                                    <i class="fas fa-plus"></i>
                                </a>
                                <button type="submit" class="btn btn-sm btn-danger py-1 px-2" title="حذف"
                                        onclick="if(confirm('هل أنت متأكد من حذف هذه المتابعة؟')) { var form = document.createElement('form'); form.method = 'POST'; form.action = '{{ url_for('delete_camera_check', id=check.id) }}'; document.body.appendChild(form); form.submit(); }">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-video fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد متابعات كاميرات مسجلة لهذا النادي</h5>
            <a href="{{ url_for('new_camera_check_for_club', club_id=club.id) }}" class="btn btn-primary mt-2">
                <i class="fas fa-plus-circle me-1"></i> إضافة متابعة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
