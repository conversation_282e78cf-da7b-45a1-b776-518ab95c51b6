{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h1 class="mb-1">إغلاق العطل الحرج</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('critical_issues_list') }}">الأعطال الحرجة</a></li>
                <li class="breadcrumb-item active" aria-current="page">إغلاق العطل</li>
            </ol>
        </nav>
    </div>
    <a href="{{ url_for('critical_issue_detail', id=issue.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> العودة إلى التفاصيل
    </a>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i> إغلاق العطل الحرج</h5>
            </div>
            <div class="card-body">
                <!-- معلومات العطل -->
                <div class="alert alert-info mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>النادي:</strong> {{ issue.club.name }}
                        </div>
                        <div class="col-md-4">
                            <strong>المرفق:</strong> غير محدد
                        </div>
                        <div class="col-md-4">
                            <strong>رقم الطلب:</strong> {{ issue.ticket_number }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-4">
                            <strong>تاريخ الإنشاء:</strong> {{ issue.creation_date.strftime('%Y-%m-%d') }}
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ الاستحقاق:</strong> {{ issue.due_date.strftime('%Y-%m-%d') }}
                        </div>
                        <div class="col-md-4">
                            <strong>الحالة الحالية:</strong> {{ issue.status }}
                        </div>
                    </div>
                </div>

                <form method="post" action="{{ url_for('close_critical_issue', id=issue.id) }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="status" class="form-label">حالة الإغلاق <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="" selected disabled>اختر الحالة</option>
                                {% for status in status_options %}
                                <option value="{{ status }}">{{ status }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6" id="new_due_date_container">
                            <label for="new_due_date" class="form-label">تاريخ الاستحقاق الجديد <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="new_due_date" name="new_due_date" lang="en">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات الإغلاق</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4">{{ issue.notes }}</textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg px-5">
                            <i class="fas fa-check-circle me-1"></i> إغلاق العطل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block head %}
<style>
    /* تنسيق حقول التاريخ */
    input[type="date"] {
        font-family: 'Segoe UI', Arial, sans-serif !important;
        direction: ltr !important;
        height: 50px;
        font-size: 16px;
        text-align: center;
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 0.375rem 0.75rem;
        background-color: #fff;
        cursor: pointer;
        width: 100%;
    }

    /* إخفاء أيقونة التقويم الافتراضية */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        cursor: pointer;
    }

    /* تنسيق أيقونة التقويم المخصصة */
    .input-group-text {
        background-color: #f0f0f0;
        cursor: pointer;
    }

    .input-group-text:hover {
        background-color: #e0e0e0;
    }

    .input-group-text .fas {
        color: #0d6efd;
        font-size: 18px;
    }

    /* تنسيق لإظهار الأرقام باللغة الإنجليزية */
    input[type="date"]::-webkit-datetime-edit {
        font-family: 'Segoe UI', Arial, sans-serif !important;
        direction: ltr !important;
    }

    /* تنسيق حقل التاريخ النشط */
    .active-field input[type="date"] {
        border-color: #28a745;
        border-width: 2px;
    }

    .active-field label {
        color: #28a745;
        font-weight: bold;
    }

    /* إضافة أيقونة لحقل التاريخ النشط */
    .active-field label::after {
        content: " ✓";
        color: #28a745;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة مستمع للنقر على أيقونة التقويم
        document.querySelectorAll('.input-group-text').forEach(function(icon) {
            icon.addEventListener('click', function() {
                document.getElementById('new_due_date').showPicker();
            });
        });

        // تعيين لغة حقل التاريخ للإنجليزية
        const newDueDateInput = document.getElementById('new_due_date');

        // تعيين اللغة والسمات الإضافية
        newDueDateInput.lang = 'en';

        // إضافة مستمع الحدث للنقر لفتح منتقي التاريخ
        newDueDateInput.addEventListener('click', function() {
            this.showPicker();
        });

        // إظهار/إخفاء حقل تاريخ الاستحقاق الجديد بناءً على الحالة المختارة
        const statusSelect = document.getElementById('status');
        const newDueDateContainer = document.getElementById('new_due_date_container');
        const newDueDateInput = document.getElementById('new_due_date');

        // وظيفة لتحديث حالة حقل التاريخ
        function updateDueDateField() {
            if (statusSelect.value === 'ترحيل تاريخ الاستحقاق') {
                // تفعيل حقل التاريخ
                newDueDateInput.disabled = false;
                newDueDateInput.required = true;
                newDueDateContainer.classList.add('active-field');

                // تعيين تاريخ افتراضي إذا كان الحقل فارغاً
                if (!newDueDateInput.value) {
                    // تعيين تاريخ الغد كتاريخ افتراضي
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const formattedDate = tomorrow.toISOString().split('T')[0];
                    newDueDateInput.value = formattedDate;
                }
            } else {
                // تعطيل حقل التاريخ
                newDueDateInput.disabled = true;
                newDueDateInput.required = false;
                newDueDateContainer.classList.remove('active-field');
            }
        }

        // إضافة مستمع الحدث لتغيير الحالة
        statusSelect.addEventListener('change', updateDueDateField);

        // تشغيل التحقق عند تحميل الصفحة
        updateDueDateField();

        // إضافة مستمع الحدث للنموذج للتحقق قبل الإرسال
        document.querySelector('form').addEventListener('submit', function(event) {
            if (statusSelect.value === 'ترحيل تاريخ الاستحقاق' && !newDueDateInput.value) {
                event.preventDefault();
                alert('يرجى تحديد تاريخ الاستحقاق الجديد');
            }
        });
    });
</script>
{% endblock %}
