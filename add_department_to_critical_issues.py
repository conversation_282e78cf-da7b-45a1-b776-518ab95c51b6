#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة عمود القسم المختص إلى جدول الأعطال الحرجة
"""

import sqlite3
import os

def add_department_column():
    """إضافة عمود القسم المختص إلى جدول critical_issue"""
    
    # التحقق من وجود ملف قاعدة البيانات
    db_path = 'app.db'
    if not os.path.exists(db_path):
        print(f"ملف قاعدة البيانات غير موجود: {db_path}")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='critical_issue'
        """)
        
        if not cursor.fetchone():
            print("جدول critical_issue غير موجود")
            conn.close()
            return False
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(critical_issue)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'department' in columns:
            print("عمود القسم المختص موجود بالفعل")
            conn.close()
            return True
        
        # إضافة العمود
        cursor.execute("""
            ALTER TABLE critical_issue 
            ADD COLUMN department VARCHAR(100)
        """)
        
        # حفظ التغييرات
        conn.commit()
        print("تم إضافة عمود القسم المختص بنجاح")
        
        # التحقق من إضافة العمود
        cursor.execute("PRAGMA table_info(critical_issue)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'department' in columns:
            print("تم التحقق من إضافة العمود بنجاح")
            success = True
        else:
            print("فشل في إضافة العمود")
            success = False
        
        conn.close()
        return success
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("بدء إضافة عمود القسم المختص...")
    
    if add_department_column():
        print("تم إنجاز العملية بنجاح!")
    else:
        print("فشلت العملية!")
