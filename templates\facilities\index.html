{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">المرافق</h1>
    <div>
        {% if current_user.has_permission('add_facility') %}
        <a href="{{ url_for('import_facilities_excel') }}" class="btn btn-success me-2">
            <i class="fas fa-file-excel me-1"></i> استيراد من إكسل
        </a>
        <a href="{{ url_for('new_facility') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة مرفق جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <form action="{{ url_for('facilities') }}" method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="ابحث عن مرفق..." name="search" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                {% if search_query %}
                <a href="{{ url_for('facilities') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> مسح البحث
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if facilities %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="60%">اسم المرفق</th>
                        <th class="text-center" width="15%">الحالة</th>
                        <th class="text-center" width="20%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for facility in facilities %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>
                            <a href="{{ url_for('facility_detail', id=facility.id) }}" class="text-decoration-none fw-bold text-primary">
                                {{ facility.name }}
                            </a>
                            <div class="mt-1">
                                <a href="{{ url_for('facility_items', facility_id=facility.id) }}" class="text-decoration-none text-muted small">
                                    <i class="fas fa-list me-1"></i> عرض البنود
                                </a>
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="badge {% if facility.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                {% if facility.is_active %}نشط{% else %}معطل{% endif %}
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('facility_detail', id=facility.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_permission('edit_facility') %}
                                <a href="{{ url_for('edit_facility', id=facility.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('delete_facility') %}
                                <a href="{{ url_for('delete_facility', id=facility.id) }}" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف المرفق \'{{ facility.name }}\' ؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('edit_facility') %}
                                <button type="button" class="btn btn-sm {% if facility.is_active %}btn-outline-success active{% else %}btn-outline-danger{% endif %} toggle-btn toggle-facility-btn" data-facility-id="{{ facility.id }}" data-status="{% if facility.is_active %}active{% else %}inactive{% endif %}" title="{% if facility.is_active %}تعطيل{% else %}تفعيل{% endif %}">
                                    <i class="fas {% if facility.is_active %}fa-toggle-on{% else %}fa-toggle-off{% endif %} me-1"></i>
                                    <span>{% if facility.is_active %}نشط{% else %}معطل{% endif %}</span>
                                </button>
                                {% endif %}
                            </div>

                            <!-- Modal for Delete Confirmation -->
                            <!-- تم نقل المودال خارج الجدول لتجنب مشكلة الاهتزاز -->
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="display-1 text-muted mb-3">
                <i class="fas fa-box-open"></i>
            </div>
            <h4>لا توجد مرافق حالياً</h4>
            <p class="text-muted">قم بإضافة مرافق جديدة أو استيراد من ملف إكسل</p>
            <div class="mt-4">
                <a href="{{ url_for('new_facility') }}" class="btn btn-primary me-2">
                    <i class="fas fa-plus"></i> إضافة مرفق جديد
                </a>
                <a href="{{ url_for('import_facilities_excel') }}" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> استيراد من إكسل
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>


{% endblock %}


