{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">تفاصيل نوع المخالفة</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_clubs_list') }}">سجل المخالفات</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violation_types_list') }}">أنواع المخالفات</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ violation_type.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('edit_violation_type', id=violation_type.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="{{ url_for('violation_types_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i> معلومات نوع المخالفة</h4>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="30%">الاسم</th>
                        <td>{{ violation_type.name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الوصف</th>
                        <td>{{ violation_type.description or 'لا يوجد وصف' }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الحالة</th>
                        <td>
                            {% if violation_type.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">مستورد</th>
                        <td>
                            {% if violation_type.is_imported %}
                            <span class="badge bg-info">نعم</span>
                            {% else %}
                            <span class="badge bg-secondary">لا</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">عدد المخالفات المرتبطة</th>
                        <td>{{ violations_count }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الإنشاء</th>
                        <td>{{ violation_type.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">آخر تحديث</th>
                        <td>{{ violation_type.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                </table>
                
                {% if violations_count == 0 %}
                <div class="mt-4">
                    <form action="{{ url_for('delete_violation_type', id=violation_type.id) }}" method="post" onsubmit="return confirm('هل أنت متأكد من حذف نوع المخالفة؟');">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> حذف نوع المخالفة
                        </button>
                    </form>
                </div>
                {% else %}
                <div class="alert alert-warning mt-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا يمكن حذف نوع المخالفة لأنه مرتبط بـ {{ violations_count }} مخالفة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
