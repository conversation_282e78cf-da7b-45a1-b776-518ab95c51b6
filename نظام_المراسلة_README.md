# نظام المراسلة - دليل المستخدم

## نظرة عامة

تم إنشاء نظام مراسلة شامل في التطبيق يتيح للمستخدمين التواصل فيما بينهم بطريقة مشابهة لنظام البريد الإلكتروني. يدعم النظام إرسال الرسائل الفردية والجماعية، المرفقات، الردود، وتتبع حالة القراءة.

## الميزات الرئيسية

### 1. إرسال الرسائل
- **رسائل فردية**: إرسال رسائل لمستخدمين محددين
- **رسائل جماعية**: إرسال رسائل لمجموعة من المستخدمين
- **رسائل عامة**: إرسال رسائل لجميع المستخدمين (للمديرين فقط)
- **رسائل حسب الدور**: إرسال رسائل لجميع المستخدمين في دور معين

### 2. أنواع الرسائل
- **عامة**: رسائل عادية للتواصل اليومي
- **إشعار**: رسائل إعلامية مهمة
- **تنبيه**: رسائل عاجلة تتطلب انتباه فوري

### 3. مستويات الأولوية
- **عالية**: رسائل عاجلة ومهمة
- **عادية**: رسائل اعتيادية
- **منخفضة**: رسائل أقل أهمية

### 4. المرفقات
- دعم أنواع ملفات متعددة: TXT, PDF, PNG, JPG, JPEG, GIF, DOC, DOCX, XLS, XLSX
- إمكانية إرفاق ملفات متعددة في رسالة واحدة
- عرض معلومات الملف (الاسم، الحجم، تاريخ الرفع)
- تحميل المرفقات بأمان

### 5. الردود
- الرد على الرسائل مع الاحتفاظ بالسياق
- عرض سلسلة الردود بشكل منظم
- إمكانية إرفاق ملفات في الردود
- رد سريع من صفحة عرض الرسالة

### 6. تتبع حالة القراءة
- تحديد الرسائل كمقروءة/غير مقروءة
- عرض تاريخ ووقت القراءة
- إحصائيات للمرسل حول عدد المستقبلين الذين قرأوا الرسالة

## الصلاحيات

### صلاحيات نظام المراسلة
- **view_messages**: عرض الرسائل
- **send_message**: إرسال رسائل جديدة
- **reply_message**: الرد على الرسائل
- **delete_message**: حذف الرسائل من صندوق الوارد
- **send_broadcast_message**: إرسال رسائل عامة لجميع المستخدمين
- **manage_all_messages**: إدارة جميع الرسائل (للمديرين)

### توزيع الصلاحيات حسب الأدوار
- **المسؤول (admin)**: جميع الصلاحيات
- **المدير (manager)**: عرض، إرسال، رد، إرسال عام
- **المشرف (supervisor)**: عرض، إرسال، رد
- **المستخدم (user)**: عرض، إرسال، رد

## كيفية الاستخدام

### 1. الوصول لنظام المراسلة
- من شريط التنقل العلوي، انقر على "المراسلة"
- ستظهر قائمة منسدلة تحتوي على:
  - صندوق الوارد
  - الرسائل المرسلة
  - رسالة جديدة

### 2. صندوق الوارد
- عرض جميع الرسائل الواردة
- تصفية الرسائل حسب النوع والحالة
- البحث في الرسائل
- عرض عدد الرسائل غير المقروءة في شريط التنقل

### 3. إنشاء رسالة جديدة
1. انقر على "رسالة جديدة" أو "+" في صندوق الوارد
2. املأ الحقول المطلوبة:
   - موضوع الرسالة
   - محتوى الرسالة
   - اختر الأولوية ونوع الرسالة
   - حدد المستقبلين
3. أرفق ملفات إذا لزم الأمر
4. انقر على "إرسال الرسالة"

### 4. قراءة الرسائل
- انقر على الرسالة في صندوق الوارد لعرضها
- ستُحدد الرسالة تلقائياً كمقروءة
- يمكن تحميل المرفقات من صفحة عرض الرسالة

### 5. الرد على الرسائل
- من صفحة عرض الرسالة، انقر على "رد"
- أو استخدم نموذج الرد السريع في أسفل الصفحة
- اكتب ردك وأرفق ملفات إذا لزم الأمر
- انقر على "إرسال الرد"

### 6. حذف الرسائل
- من صندوق الوارد، انقر على أيقونة الحذف بجانب الرسالة
- أو من صفحة عرض الرسالة، انقر على "حذف من صندوق الوارد"
- الحذف يخفي الرسالة من صندوق الوارد فقط ولا يحذفها نهائياً

## الميزات التقنية

### 1. قاعدة البيانات
- **message**: جدول الرسائل الرئيسي
- **message_recipient**: جدول المستقبلين مع حالة القراءة
- **message_attachment**: جدول المرفقات

### 2. الأمان
- التحقق من الصلاحيات قبل كل عملية
- حماية المرفقات من الوصول غير المصرح
- تشفير أسماء الملفات المرفوعة

### 3. الأداء
- فهرسة الجداول لتحسين سرعة البحث
- تحديث عدد الرسائل غير المقروءة تلقائياً
- تصفح الرسائل مع دعم التقسيم

### 4. تجربة المستخدم
- واجهة عربية متجاوبة
- تحديث فوري لحالة القراءة
- معاينة المرفقات قبل الإرسال
- حفظ المسودات تلقائياً

## الملفات المضافة

### 1. قاعدة البيانات
- `create_messages_system.py`: سكريبت إنشاء جداول نظام المراسلة
- `create_sample_messages.py`: سكريبت إنشاء رسائل تجريبية

### 2. النماذج (Models)
- تم إضافة النماذج في `app.py`:
  - `Message`: نموذج الرسالة
  - `MessageRecipient`: نموذج المستقبل
  - `MessageAttachment`: نموذج المرفق

### 3. المسارات (Routes)
- `app/routes/messages.py`: جميع مسارات نظام المراسلة

### 4. القوالب (Templates)
- `templates/messages/inbox.html`: صندوق الوارد
- `templates/messages/compose.html`: إنشاء رسالة جديدة
- `templates/messages/view.html`: عرض الرسالة
- `templates/messages/reply.html`: الرد على الرسالة
- `templates/messages/sent.html`: الرسائل المرسلة

### 5. التحديثات
- تحديث `templates/base.html` لإضافة رابط نظام المراسلة
- إضافة فلتر `nl2br` لتحويل أسطر النص الجديدة إلى `<br>`

## التشغيل والاختبار

1. تشغيل سكريبت إنشاء النظام:
   ```bash
   python create_messages_system.py
   ```

2. إنشاء رسائل تجريبية:
   ```bash
   python create_sample_messages.py
   ```

3. تشغيل التطبيق:
   ```bash
   python app.py
   ```

4. فتح المتصفح والانتقال إلى: `http://localhost:5000`

5. تسجيل الدخول والانتقال إلى قسم "المراسلة"

## الدعم والصيانة

- يتم حفظ المرفقات في مجلد `uploads/messages/`
- يمكن مراقبة استخدام النظام من خلال جداول قاعدة البيانات
- يُنصح بعمل نسخ احتياطية دورية لقاعدة البيانات والمرفقات

---

تم إنشاء هذا النظام بواسطة Augment Agent لتحسين التواصل الداخلي في التطبيق.
