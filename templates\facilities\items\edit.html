{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>تعديل بند</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facilities') }}">المرافق</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facility_detail', id=facility.id) }}">{{ facility.name }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facility_items', facility_id=facility.id) }}">البنود</a></li>
                <li class="breadcrumb-item active" aria-current="page">تعديل بند</li>
            </ol>
        </nav>
    </div>
    <a href="{{ url_for('facility_items', facility_id=facility.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة إلى قائمة البنود
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-edit me-2"></i> تعديل بند في {{ facility.name }}</h4>
                <span class="badge bg-dark">رقم {{ item.id }}</span>
            </div>
            <div class="card-body">
                <form action="" method="post" novalidate>
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم البند <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <input type="text" class="form-control" id="name" name="name" value="{{ item.name }}" required>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        الحقول المعلمة بعلامة <span class="text-danger">*</span> إلزامية
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('facility_items', facility_id=facility.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> معلومات البند</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="display-4 text-primary">
                        <i class="fas fa-tag"></i>
                    </div>
                    <h4>{{ item.name }}</h4>
                </div>

                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>الحالة:</span>
                        {% if item.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-danger">معطل</span>
                        {% endif %}
                    </li>
                </ul>

                <div class="mt-3">
                    <form action="{{ url_for('toggle_facility_item', facility_id=facility.id, item_id=item.id) }}" method="post">
                        <button type="submit" class="btn btn-block w-100 {% if item.is_active %}btn-outline-success active{% else %}btn-outline-danger{% endif %} toggle-btn">
                            <i class="fas {% if item.is_active %}fa-toggle-on{% else %}fa-toggle-off{% endif %} me-1"></i>
                            {% if item.is_active %}تعطيل البند{% else %}تفعيل البند{% endif %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
