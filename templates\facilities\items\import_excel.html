{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>استيراد بنود من إكسل</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facilities') }}">المرافق</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facility_detail', id=facility.id) }}">{{ facility.name }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facility_items', facility_id=facility.id) }}">البنود</a></li>
                <li class="breadcrumb-item active" aria-current="page">استيراد من إكسل</li>
            </ol>
        </nav>
    </div>
    <a href="{{ url_for('facility_items', facility_id=facility.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة إلى قائمة البنود
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">تحميل ملف إكسل</h5>
            </div>
            <div class="card-body">
                <form action="" method="post" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="file" class="form-label">اختر ملف إكسل</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls,.csv" required>
                        <div class="form-text">
                            يجب أن يحتوي الملف على العمود التالي: name (إلزامي)
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6 class="alert-heading"><i class="fas fa-info-circle"></i> ملاحظات هامة:</h6>
                        <ul class="mb-0">
                            <li>سيتم تجاهل البنود الموجودة مسبقاً في هذا المرفق.</li>
                            <li>سيتم إضافة البنود الجديدة تلقائياً.</li>
                            <li>الحد الأقصى لحجم الملف هو 16 ميجابايت.</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('download_facility_items_template') }}" class="btn btn-success">
                            <i class="fas fa-download"></i> تنزيل قالب إكسل
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> رفع الملف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">تعليمات الاستيراد</h5>
            </div>
            <div class="card-body">
                <h6 class="fw-bold">خطوات الاستيراد:</h6>
                <ol>
                    <li>قم بتنزيل قالب إكسل من الزر أدناه.</li>
                    <li>املأ البيانات في القالب وفقاً للأعمدة المطلوبة.</li>
                    <li>احفظ الملف بتنسيق Excel (.xlsx) أو CSV.</li>
                    <li>قم بتحميل الملف باستخدام النموذج المجاور.</li>
                </ol>
                
                <h6 class="fw-bold mt-4">الأعمدة المطلوبة:</h6>
                <ul>
                    <li><strong>name:</strong> اسم البند (إلزامي)</li>
                </ul>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكد من عدم وجود أخطاء إملائية في أسماء البنود
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
