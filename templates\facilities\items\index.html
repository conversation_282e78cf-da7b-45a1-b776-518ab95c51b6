{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h1 class="mb-1">بنود {{ facility.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facilities') }}">المرافق</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('facility_detail', id=facility.id) }}">{{ facility.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">البنود</li>
            </ol>
        </nav>
    </div>
    <div>
        {% if current_user.has_permission('import_facility_items_excel') %}
        <a href="{{ url_for('import_facility_items_excel', facility_id=facility.id) }}" class="btn btn-success me-2">
            <i class="fas fa-file-excel me-1"></i> استيراد من إكسل
        </a>
        {% endif %}
        {% if current_user.has_permission('add_facility_item') or current_user.is_admin %}
        <a href="{{ url_for('new_facility_item', facility_id=facility.id) }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة بند جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <form action="{{ url_for('facility_items', facility_id=facility.id) }}" method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="ابحث عن بند..." name="search" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                {% if search_query %}
                <a href="{{ url_for('facility_items', facility_id=facility.id) }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> مسح البحث
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i> قائمة البنود</h5>
        <span class="badge bg-light text-primary">{{ items|length }} بند</span>
    </div>
    <div class="card-body">
        {% if items %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="50%">اسم البند</th>
                        <th class="text-center" width="15%">الحالة</th>
                        <th class="text-center" width="30%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>{{ item.name }}</td>
                        <td class="text-center">
                            {% if item.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">معطل</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                {% if current_user.has_permission('edit_facility_item') or current_user.is_admin %}
                                <a href="{{ url_for('edit_facility_item', facility_id=facility.id, item_id=item.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('delete_facility_item') or current_user.is_admin %}
                                <a href="{{ url_for('delete_facility_item', facility_id=facility.id, item_id=item.id) }}" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف البند \'{{ item.name }}\' ؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('toggle_facility_item') or current_user.is_admin %}
                                <button type="button" class="btn btn-sm {% if item.is_active %}btn-outline-success active{% else %}btn-outline-danger{% endif %} toggle-btn toggle-item-btn" data-facility-id="{{ facility.id }}" data-item-id="{{ item.id }}" data-status="{% if item.is_active %}active{% else %}inactive{% endif %}" title="{% if item.is_active %}تعطيل{% else %}تفعيل{% endif %}">
                                    <i class="fas {% if item.is_active %}fa-toggle-on{% else %}fa-toggle-off{% endif %} me-1"></i>
                                    <span>{% if item.is_active %}نشط{% else %}معطل{% endif %}</span>
                                </button>
                                {% endif %}
                            </div>

                            <!-- Modal for Delete Confirmation -->
                            <!-- تم نقل المودال خارج الجدول لتجنب مشكلة الاهتزاز -->
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="display-1 text-muted mb-3">
                <i class="fas fa-box-open"></i>
            </div>
            <h4>لا توجد بنود حالياً</h4>
            <p class="text-muted">قم بإضافة بنود جديدة أو استيراد من ملف إكسل</p>
            <div class="mt-4">
                {% if current_user.has_permission('add_facility_item') or current_user.is_admin %}
                <a href="{{ url_for('new_facility_item', facility_id=facility.id) }}" class="btn btn-primary me-2">
                    <i class="fas fa-plus"></i> إضافة بند جديد
                </a>
                {% endif %}
                {% if current_user.has_permission('import_facility_items_excel') or current_user.is_admin %}
                <a href="{{ url_for('import_facility_items_excel', facility_id=facility.id) }}" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> استيراد من إكسل
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>


{% endblock %}
