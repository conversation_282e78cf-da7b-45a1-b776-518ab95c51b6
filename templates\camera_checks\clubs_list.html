{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">سجل متابعة الكاميرات</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item active" aria-current="page">سجل متابعة الكاميرات</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('new_camera_check') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> متابعة كاميرات جديدة
        </a>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light py-3">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i> قائمة الأندية</h5>
            </div>
            <div class="col-md-4">
                <form action="{{ url_for('camera_checks_clubs_list') }}" method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{{ url_for('camera_checks_clubs_list') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if clubs %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="30%">اسم النادي</th>
                        <th class="text-center" width="15%">عدد التشيكات لشهر ({{ current_month_name }})</th>
                        <th class="text-center" width="15%">تاريخ آخر متابعة</th>
                        <th class="text-center" width="20%">عدد المخالفات ({{ current_month_name }})</th>
                        <th class="text-center" width="15%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for club in clubs %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>{{ club.name }}</td>
                        <td class="text-center">{{ club.camera_checks_count }}</td>
                        <td class="text-center">
                            {% if club.last_check_date %}
                                {{ club.last_check_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center">{{ club.current_month_violations }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('camera_checks_club_detail', club_id=club.id) }}" class="btn btn-info btn-sm" title="عرض المتابعات">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('new_camera_check_for_club', club_id=club.id) }}" class="btn btn-success btn-sm" title="إضافة متابعة جديدة">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-building fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أندية للعرض</h5>
            <p class="text-muted">قم بإضافة نادي جديد للبدء</p>
            <a href="{{ url_for('new_club') }}" class="btn btn-primary mt-2">
                <i class="fas fa-plus-circle me-1"></i> إضافة نادي جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
