{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mb-1">تعديل بيانات الموظف</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('employees') }}">الموظفين</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('employee_detail', id=employee.id) }}">{{ employee.name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل</li>
                </ol>
            </nav>
        </div>
        <a href="{{ url_for('employee_detail', id=employee.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى التفاصيل
        </a>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i> تعديل بيانات الموظف</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('edit_employee', id=employee.id) }}">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="employee_id" class="form-label">الرقم الوظيفي <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                    <input type="text" class="form-control" id="employee_id" name="employee_id" required value="{{ employee.employee_id }}">
                                </div>
                                <small class="text-muted">يجب أن يكون الرقم الوظيفي فريداً</small>
                            </div>
                            <div class="col-md-6">
                                <label for="name" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="name" name="name" required value="{{ employee.name }}">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="position" class="form-label">الوظيفة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                    <select class="form-select" id="position" name="position" required>
                                        <option value="" disabled>اختر الوظيفة</option>
                                        <option value="خدمة عملاء" {% if employee.position == 'موظف خدمة عملاء' or employee.position == 'خدمة عملاء' %}selected{% endif %}>خدمة عملاء</option>
                                        <option value="مدرب" {% if employee.position == 'مدرب' %}selected{% endif %}>مدرب</option>
                                        <option value="عامل" {% if employee.position == 'عامل' %}selected{% endif %}>عامل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="role" class="form-label">الدور الوظيفي <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="" disabled>اختر الدور الوظيفي</option>
                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="trainer_type" class="form-label">نوع المدرب</label>
                                <div class="trainer-type-container" id="trainer_type_container" style="display: none;">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input trainer-toggle-wide" type="checkbox" id="trainer_type_toggle" name="trainer_type_toggle" {% if employee.trainer_type == 'متفرغ' %}checked{% endif %}>
                                            <input type="hidden" id="trainer_type" name="trainer_type" value="{{ employee.trainer_type or 'غير متفرغ' }}">
                                        </div>
                                        <small id="trainer_status_text" class="badge {% if employee.trainer_type == 'متفرغ' %}bg-success{% else %}bg-secondary{% endif %}">{{ employee.trainer_type or 'غير متفرغ' }}</small>
                                    </div>
                                </div>
                                <div class="trainer-type-placeholder" id="trainer_type_placeholder">
                                    <input type="text" class="form-control" value="غير متاح" readonly style="background-color: #f8f9fa; color: #6c757d;">
                                </div>
                            </div>
                        </div>

                        <style>
                            /* تحسين شكل زر التبديل */
                            .trainer-toggle-wide {
                                width: 80px !important;
                                height: 25px !important;
                                border-radius: 12px !important;
                            }

                            .trainer-toggle-wide:checked {
                                background-color: #28a745 !important;
                                border-color: #28a745 !important;
                            }

                            .trainer-toggle-wide:not(:checked) {
                                background-color: #6c757d !important;
                                border-color: #6c757d !important;
                            }

                            .trainer-toggle-wide:focus {
                                box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
                            }

                            .trainer-type-container {
                                padding: 15px;
                                border: 1px solid #dee2e6;
                                border-radius: 8px;
                                background-color: #f8f9fa;
                                min-height: 60px;
                                display: flex;
                                align-items: center;
                            }
                        </style>

                        <script>
                            // قواميس الأدوار الوظيفية لكل وظيفة
                            const rolesByPosition = {
                                'خدمة عملاء': [
                                    'مدير نادي',
                                    'نائب مدير نادي مكلف',
                                    'خدمة عملاء',
                                    'اختصاصي تسويق',
                                    'منسق عمليات'
                                ],
                                'مدرب': [
                                    'مدرب كمال أجسام',
                                    'مدرب سباحة',
                                    'مدرب لياقة',
                                    'مدير لياقة',
                                    'مدرب رياضي',
                                    'مدرب لياقة وكمال اجسام',
                                    'مساعد مدرب'
                                ],
                                'عامل': [
                                    'مشرف عمال',
                                    'عامل نظافة'
                                ]
                            };

                            // الأدوار التي تتطلب إظهار حقل نوع المدرب
                            const trainerRoles = [
                                'مدرب كمال أجسام',
                                'مدرب سباحة',
                                'مدرب لياقة',
                                'مدرب رياضي',
                                'مدرب لياقة وكمال اجسام',
                                'مساعد مدرب'
                            ];

                            // تحديث خيارات الدور الوظيفي بناءً على الوظيفة المحددة
                            function updateRoles() {
                                const positionSelect = document.getElementById('position');
                                const roleSelect = document.getElementById('role');
                                const selectedPosition = positionSelect.value;
                                const currentRole = "{{ employee.role }}";

                                // إفراغ قائمة الأدوار الوظيفية
                                roleSelect.innerHTML = '<option value="" disabled>اختر الدور الوظيفي</option>';

                                // إضافة الأدوار الوظيفية المناسبة
                                if (selectedPosition && rolesByPosition[selectedPosition]) {
                                    rolesByPosition[selectedPosition].forEach(role => {
                                        const option = document.createElement('option');
                                        option.value = role;
                                        option.textContent = role;
                                        if (role === currentRole) {
                                            option.selected = true;
                                        }
                                        roleSelect.appendChild(option);
                                    });
                                }

                                // إذا لم يتم تحديد أي دور وظيفي، أضف الدور الحالي
                                if (roleSelect.selectedIndex === -1 && currentRole) {
                                    const option = document.createElement('option');
                                    option.value = currentRole;
                                    option.textContent = currentRole;
                                    option.selected = true;
                                    roleSelect.appendChild(option);
                                }

                                // تحديث رؤية حقل نوع المدرب
                                updateTrainerTypeVisibility();
                            }

                            // إظهار/إخفاء حقل نوع المدرب بناءً على الدور الوظيفي
                            function updateTrainerTypeVisibility() {
                                const roleSelect = document.getElementById('role');
                                const trainerTypeContainer = document.getElementById('trainer_type_container');
                                const trainerTypePlaceholder = document.getElementById('trainer_type_placeholder');
                                const selectedRole = roleSelect.value;

                                if (trainerRoles.includes(selectedRole)) {
                                    // إظهار حقل نوع المدرب
                                    trainerTypeContainer.style.display = 'block';
                                    trainerTypePlaceholder.style.display = 'none';
                                } else {
                                    // إخفاء حقل نوع المدرب
                                    trainerTypeContainer.style.display = 'none';
                                    trainerTypePlaceholder.style.display = 'block';
                                    // إعادة تعيين القيم للحالة الافتراضية
                                    resetTrainerToggle();
                                }
                            }

                            // إعادة تعيين زر التبديل للحالة الافتراضية
                            function resetTrainerToggle() {
                                const toggle = document.getElementById('trainer_type_toggle');
                                const hiddenInput = document.getElementById('trainer_type');
                                const statusText = document.getElementById('trainer_status_text');

                                toggle.checked = false;
                                hiddenInput.value = 'غير متفرغ';
                                statusText.textContent = 'غير متفرغ';
                                statusText.className = 'badge bg-secondary';
                            }

                            // تحديث حالة نوع المدرب عند تغيير الزر
                            function updateTrainerType() {
                                const toggle = document.getElementById('trainer_type_toggle');
                                const hiddenInput = document.getElementById('trainer_type');
                                const statusText = document.getElementById('trainer_status_text');

                                if (toggle.checked) {
                                    // متفرغ - مفعل
                                    hiddenInput.value = 'متفرغ';
                                    statusText.textContent = 'متفرغ';
                                    statusText.className = 'badge bg-success';
                                } else {
                                    // غير متفرغ - معطل
                                    hiddenInput.value = 'غير متفرغ';
                                    statusText.textContent = 'غير متفرغ';
                                    statusText.className = 'badge bg-secondary';
                                }
                            }

                            // تسجيل أحداث التغيير
                            document.getElementById('position').addEventListener('change', updateRoles);
                            document.getElementById('role').addEventListener('change', updateTrainerTypeVisibility);
                            document.getElementById('trainer_type_toggle').addEventListener('change', updateTrainerType);

                            // تهيئة القوائم عند تحميل الصفحة
                            document.addEventListener('DOMContentLoaded', function() {
                                updateRoles();
                                updateTrainerTypeVisibility();
                                updateTrainerType(); // تهيئة حالة زر التبديل
                            });
                        </script>

                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ employee.phone or '' }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="club_id" class="form-label">النادي <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-building"></i></span>
                                <select class="form-select" id="club_id" name="club_id" required>
                                    {% for club in clubs %}
                                    <option value="{{ club.id }}" {% if club.id == employee.club_id %}selected{% endif %}>{{ club.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if employee.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    الموظف نشط
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
