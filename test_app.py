#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("بدء تشغيل التطبيق...")
    print("تحميل الوحدات...")
    
    from app import app, db
    
    print("تم تحميل التطبيق بنجاح")
    print("التحقق من قاعدة البيانات...")
    
    # التحقق من وجود قاعدة البيانات
    if os.path.exists('app.db'):
        print("قاعدة البيانات موجودة")
    else:
        print("قاعدة البيانات غير موجودة - سيتم إنشاؤها")
    
    print("بدء تشغيل الخادم...")
    print("يمكنك الوصول للتطبيق على: http://localhost:5000")
    print("للإيقاف اضغط Ctrl+C")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من تثبيت جميع المتطلبات")
except Exception as e:
    print(f"خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()
