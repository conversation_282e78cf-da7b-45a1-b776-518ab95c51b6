{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-user-shield me-2"></i>إدارة صلاحيات المستخدم</h4>
                    <a href="{{ url_for('user_detail', id=user.id) }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>العودة
                    </a>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="user-info p-3 bg-light rounded">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-info-circle me-2"></i>معلومات المستخدم</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">الاسم</small>
                                                <strong>{{ user.name }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-id-badge"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">اسم المستخدم</small>
                                                <strong>{{ user.username }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-user-tag"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">الدور</small>
                                                {% if user.role == 'admin' %}
                                                <span class="badge bg-danger">مسؤول النظام</span>
                                                {% elif user.role == 'manager' %}
                                                <span class="badge bg-warning text-dark">مدير</span>
                                                {% elif user.role == 'supervisor' %}
                                                <span class="badge bg-info">مشرف</span>
                                                {% else %}
                                                <span class="badge bg-success">مستخدم عادي</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="p-3 bg-light rounded">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-info-circle me-2"></i>معلومات الصلاحيات</h5>
                                <p>يمكنك تحديد الصلاحيات المخصصة لهذا المستخدم. إذا لم يتم تحديد أي صلاحيات، سيتم استخدام الصلاحيات الافتراضية للدور.</p>
                                <div class="d-flex align-items-center mt-3">
                                    <div class="icon-box bg-warning text-dark rounded-circle p-2 me-2">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">ملاحظة هامة</small>
                                        <span>المسؤول لديه جميع الصلاحيات تلقائياً بغض النظر عن الإعدادات أدناه.</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('user_permissions', id=user.id) }}">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-shield-alt me-2"></i>الصلاحيات المتاحة</h5>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAll">تحديد الكل</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">إلغاء تحديد الكل</button>
                                    </div>
                                </div>
                                <hr>
                            </div>
                        </div>

                        <!-- مجموعات الصلاحيات -->
                        <div class="row">
                            <!-- إدارة المستخدمين -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_users') or permission.code.startswith('add_user') or permission.code.startswith('edit_user') or permission.code.startswith('delete_user') or permission.code.startswith('manage_user_permissions') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة النوادي -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-building me-2"></i>إدارة النوادي</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_clubs') or permission.code.startswith('add_club') or permission.code.startswith('edit_club') or permission.code.startswith('delete_club') or permission.code.startswith('manage_club_facilities') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة المرافق -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>إدارة المرافق</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_facilities') or permission.code.startswith('add_facility') or permission.code.startswith('edit_facility') or permission.code.startswith('delete_facility') or permission.code.startswith('manage_facility_items') or permission.code == 'edit_facility_item' or permission.code == 'delete_facility_item' or permission.code == 'toggle_facility_item' or permission.code == 'add_facility_item' %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة الموظفين -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>إدارة الموظفين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_employees') or permission.code.startswith('add_employee') or permission.code.startswith('edit_employee') or permission.code.startswith('delete_employee') or permission.code.startswith('manage_employee_schedule') or permission.code == 'disable_employee' %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة الجداول -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>إدارة الجداول</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_schedules') or permission.code.startswith('add_schedule') or permission.code.startswith('edit_schedule') or permission.code.startswith('delete_schedule') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة التشيك -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>إدارة التشيك</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_checks') or permission.code.startswith('add_check') or permission.code.startswith('edit_check') or permission.code.startswith('delete_check') or permission.code.startswith('view_check_details') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة الكاميرات -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-video me-2"></i>إدارة الكاميرات</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_camera_checks') or permission.code.startswith('add_camera_check') or permission.code.startswith('edit_camera_check') or permission.code.startswith('delete_camera_check') or permission.code.startswith('view_camera_check_details') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة الأعطال -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>إدارة الأعطال</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_critical_issues') or permission.code.startswith('add_critical_issue') or permission.code.startswith('edit_critical_issue') or permission.code.startswith('delete_critical_issue') or permission.code.startswith('close_critical_issue') or permission.code.startswith('view_critical_issue_details') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة المبيعات -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>إدارة المبيعات</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_sales') or permission.code.startswith('add_sales_target') or permission.code.startswith('edit_sales_target') or permission.code.startswith('delete_sales_target') or permission.code.startswith('add_daily_sales') or permission.code.startswith('view_sales_details') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- التقارير -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-file-alt me-2"></i>التقارير</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_check_report') or permission.code.startswith('view_facility_report') or permission.code.startswith('view_sales_report') or permission.code.startswith('print_reports') or permission.code.startswith('export_reports') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- استيراد من الإكسل -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-file-excel me-2"></i>استيراد من الإكسل</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('import_') or permission.code == 'import_clubs_excel' %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- المخالفات -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>المخالفات</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('add_violation') or permission.code.startswith('edit_violation') or permission.code.startswith('import_violation_types') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الإجراءات النظامية -->
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>الإجراءات النظامية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="permissions-group">
                                            {% for permission in permissions %}
                                                {% if permission.code.startswith('view_procedures') or permission.code.startswith('add_procedure') or permission.code.startswith('edit_procedure') or permission.code.startswith('delete_procedure') or permission.code.startswith('view_procedure_details') %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="permissions" value="{{ permission.id }}" id="perm_{{ permission.id }}"
                                                        {% if permission in user_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        {{ permission.name }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ url_for('user_detail', id=user.id) }}" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>حفظ الصلاحيات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .icon-box {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .permissions-group {
        max-height: 250px;
        overflow-y: auto;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديد الكل
        document.getElementById('selectAll').addEventListener('click', function() {
            document.querySelectorAll('input[name="permissions"]').forEach(function(checkbox) {
                checkbox.checked = true;
            });
        });

        // إلغاء تحديد الكل
        document.getElementById('deselectAll').addEventListener('click', function() {
            document.querySelectorAll('input[name="permissions"]').forEach(function(checkbox) {
                checkbox.checked = false;
            });
        });
    });
</script>
{% endblock %}
