{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">سجل متابعة الكاميرات</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('new_camera_check') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> متابعة جديدة
        </a>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <form method="get" action="{{ url_for('camera_checks_list') }}" class="row g-2">
                    <div class="col-md-10">
                        <input type="text" name="search" class="form-control form-control-sm" placeholder="البحث عن متابعة..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-sm btn-primary w-100">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <i class="fas fa-video me-1"></i> قائمة متابعة الكاميرات
            </div>
            <div class="card-body p-0">
                {% if camera_checks %}
                <div class="table-responsive">
                    <table class="table table-sm mb-0">
                        <thead>
                            <tr>
                                <th class="text-center">#</th>
                                <th class="text-center">التاريخ</th>
                                <th class="text-center">النادي</th>
                                <th class="text-center">عدد المخالفات</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in camera_checks %}
                            <tr>
                                <td class="text-center py-2">{{ check.id }}</td>
                                <td class="text-center py-2">{{ check.check_date.strftime('%Y-%m-%d') }}</td>
                                <td class="text-center py-2">{{ check.club.name }}</td>
                                <td class="text-center py-2">{{ check.violations_count }}</td>
                                <td class="text-center py-2">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('camera_check_detail', id=check.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_camera_check', id=check.id) }}" class="btn btn-sm btn-warning py-1 px-2" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('new_camera_check_for_club', club_id=check.club.id) }}" class="btn btn-sm btn-success py-1 px-2" title="إضافة متابعة جديدة">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <!-- تم تعطيل زر الحذف مؤقتاً -->
                                        <button class="btn btn-sm btn-secondary py-1 px-2" disabled title="هذه الميزة غير متاحة حالياً">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info py-2 mb-0">
                    لا توجد متابعات كاميرات مسجلة حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
