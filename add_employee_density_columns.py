#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة أعمدة كثافة الموظفين إلى جدول النوادي
"""

import sqlite3
import os
from config import get_database_uri

def add_employee_density_columns():
    """إضافة أعمدة كثافة الموظفين إلى جدول النوادي"""
    
    # الحصول على مسار قاعدة البيانات
    db_uri = get_database_uri()
    if db_uri.startswith('sqlite:///'):
        db_path = db_uri.replace('sqlite:///', '')
    else:
        print("هذا السكريبت يدعم SQLite فقط")
        return
    
    if not os.path.exists(db_path):
        print(f"ملف قاعدة البيانات غير موجود: {db_path}")
        return
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول النوادي
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='club'")
        if not cursor.fetchone():
            print("جدول النوادي غير موجود")
            return
        
        # التحقق من وجود الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(club)")
        columns = [column[1] for column in cursor.fetchall()]
        
        columns_to_add = [
            ('target_customer_service_count', 'INTEGER DEFAULT 0'),
            ('target_trainer_count', 'INTEGER DEFAULT 0'),
            ('target_worker_count', 'INTEGER DEFAULT 0')
        ]
        
        for column_name, column_definition in columns_to_add:
            if column_name not in columns:
                print(f"إضافة العمود: {column_name}")
                cursor.execute(f"ALTER TABLE club ADD COLUMN {column_name} {column_definition}")
            else:
                print(f"العمود {column_name} موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        print("تم تحديث قاعدة البيانات بنجاح!")
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
    except Exception as e:
        print(f"خطأ عام: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    add_employee_density_columns()
