{% extends "base.html" %}

{% block head %}
<!-- إضافة تنسيق التقويم المخصص كبير الحجم -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/big-calendar.css') }}">
<style>
    /* تكبير حجم الخط في جميع الحقول */
    .form-control, .form-select, .input-group-text, .form-label {
        font-size: 18px !important;
    }

    /* تكبير حجم الخط في القوائم المنسدلة */
    select.form-select option {
        font-size: 18px !important;
    }

    /* تكبير حجم الخط في مناطق النص */
    textarea.form-control {
        font-size: 18px !important;
    }

    /* تكبير حجم الخط في العلامات والنصوص الصغيرة */
    .form-check-label, small {
        font-size: 16px !important;
    }

    /* تكبير حجم الخط في الأزرار */
    .btn {
        font-size: 18px !important;
    }

    /* زيادة ارتفاع الحقول لتناسب حجم الخط الأكبر */
    .form-control, .form-select {
        height: 45px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h1 class="mb-1">تسجيل مخالفة جديدة</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_list') }}">سجل المخالفات</a></li>
                <li class="breadcrumb-item active" aria-current="page">تسجيل مخالفة جديدة</li>
            </ol>
        </nav>
    </div>
    <div>
        {% if current_user.has_permission('import_violation_types') %}
        <a href="{{ url_for('import_violation_types') }}" class="btn btn-success me-2">
            <i class="fas fa-file-import me-1"></i> استيراد أنواع المخالفات
        </a>
        {% endif %}
        <a href="{{ url_for('violations_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى سجل المخالفات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i> تسجيل مخالفة جديدة</h4>
            </div>
            <div class="card-body">
                <form action="{{ url_for('new_violation') }}" method="post" enctype="multipart/form-data" id="violation-form">
                    <!-- حقول مخفية للبيانات المرسلة من JavaScript -->
                    <input type="hidden" name="auto_fill_js" id="auto_fill_js">
                    <input type="hidden" name="employee_data_js" id="employee_data_js">
                    <!-- حقول مخفية لتخزين بيانات الموظف -->
                    <input type="hidden" id="employee_id_hidden" name="employee_id_hidden">
                    <input type="hidden" id="employee_name_hidden" name="employee_name_hidden">
                    <input type="hidden" id="employee_role_hidden" name="employee_role_hidden">
                    <input type="hidden" id="club_name_hidden" name="club_name_hidden">
                    <input type="hidden" id="violation_number_hidden" name="violation_number_hidden">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="employee_id" class="form-label">الرقم الوظيفي <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control english-number" id="employee_id" name="employee_id" required placeholder="أدخل الرقم الوظيفي">
                                <button type="button" class="btn btn-primary" id="fetch-employee-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="employee_name" class="form-label">اسم الموظف</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="employee_name" name="employee_name">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="employee_role" class="form-label">الدور الوظيفي</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                <input type="text" class="form-control" id="employee_role" name="employee_role">
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="club_name" class="form-label">النادي</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-building"></i></span>
                                <input type="text" class="form-control" id="club_name" name="club_name">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="violation_number" class="form-label">رقم المخالفة</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                <input type="text" class="form-control english-number" id="violation_number" name="violation_number">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="violation_type_id" class="form-label">نوع المخالفة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-exclamation-triangle"></i></span>
                                <select class="form-select" id="violation_type_id" name="violation_type_id" required>
                                    <option value="" selected disabled>-- اختر نوع المخالفة --</option>
                                    {% for type in violation_types %}
                                    <option value="{{ type.id }}">{{ type.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="violation_source" class="form-label">مصدر المخالفة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user-shield"></i></span>
                                <select class="form-select" id="violation_source" name="violation_source" required>
                                    <option value="" selected disabled>-- اختر مصدر المخالفة --</option>
                                    {% for source in violation_sources %}
                                    <option value="{{ source }}">{{ source }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="violation_date" class="form-label">تاريخ المخالفة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="date" class="form-control english-input" id="violation_date" name="violation_date" required value="{{ current_date }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="is_signed" class="form-label d-block">تم التوقيع</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="is_signed" name="is_signed" style="width: 3em; height: 1.5em;">
                                <label class="form-check-label" for="is_signed">نعم، تم التوقيع على المخالفة</label>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="image" class="form-label">صورة المخالفة</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-image"></i></span>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            </div>
                            <small class="text-muted">يمكنك تحميل صورة للمخالفة (اختياري)</small>
                        </div>
                        <div class="col-md-6">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-sticky-note"></i></span>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-1"></i> حفظ المخالفة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- إضافة سكريبت التقويم المخصص كبير الحجم -->
<script src="{{ url_for('static', filename='js/big-calendar.js') }}"></script>
<!-- إضافة سكريبت البحث المباشر عن الموظف (الحل النهائي) -->
<script src="{{ url_for('static', filename='js/inline-employee-search.js') }}"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين لغة حقول التاريخ للإنجليزية
        const violationDateInput = document.getElementById('violation_date');

        // تعيين اللغة والسمات الإضافية
        if (violationDateInput) {
            violationDateInput.lang = 'en';

            // إضافة سمات إضافية للتأكد من عرض التاريخ باللغة الإنجليزية
            violationDateInput.setAttribute('data-date', '');

            // إضافة مستمع الحدث للنقر لفتح منتقي التاريخ
            violationDateInput.addEventListener('click', function() {
                this.showPicker();
            });
        }

        // التحقق من وجود بيانات موظف من JavaScript (في حالة الإضافة من صفحة الموظف)
        const autoFillJs = document.querySelector('input[name="auto_fill_js"]');
        const employeeDataJs = document.querySelector('input[name="employee_data_js"]');

        if (autoFillJs && employeeDataJs && autoFillJs.value === 'true') {
            try {
                const employeeData = JSON.parse(employeeDataJs.value);
                console.log('تم العثور على بيانات موظف من JavaScript:', employeeData);

                // ملء حقول النموذج ببيانات الموظف
                document.getElementById('employee_id').value = employeeData.employee_id;
                document.getElementById('employee_name').value = employeeData.name;
                document.getElementById('employee_role').value = employeeData.role;
                document.getElementById('club_name').value = employeeData.club_name;

                // ملء الحقول المخفية
                document.getElementById('employee_id_hidden').value = employeeData.employee_id;
                document.getElementById('employee_name_hidden').value = employeeData.name;
                document.getElementById('employee_role_hidden').value = employeeData.role;
                document.getElementById('club_name_hidden').value = employeeData.club_name;

                // إظهار رسالة تأكيد
                alert('تم ملء بيانات الموظف: ' + employeeData.name);
            } catch (e) {
                console.error('خطأ في تحليل بيانات الموظف:', e);
            }
        }

        // التحقق من وجود بيانات موظف (في حالة الإضافة من صفحة الموظف)
        {% if auto_fill and employee %}
        console.log('تم العثور على بيانات موظف:', {
            id: {{ employee.id }},
            employee_id: "{{ employee.employee_id }}",
            name: "{{ employee.name }}",
            role: "{{ employee.role }}",
            club_name: "{{ employee.club.name }}",
            violations_count: {{ violations_count }}
        });

        // إظهار رسالة تأكيد
        alert('تم العثور على بيانات الموظف: {{ employee.name }}');

        try {
            // ملء حقول النموذج ببيانات الموظف فوراً
            document.getElementById('employee_id').value = "{{ employee.employee_id }}";
            document.getElementById('employee_name').value = "{{ employee.name }}";
            document.getElementById('employee_role').value = "{{ employee.role }}";
            document.getElementById('club_name').value = "{{ employee.club.name }}";
            document.getElementById('violation_number').value = "{{ violations_count + 1 }}";

            // إظهار البيانات في وحدة التحكم للتشخيص
            console.log('تم ملء حقول النموذج:');
            console.log('الرقم الوظيفي:', document.getElementById('employee_id').value);
            console.log('اسم الموظف:', document.getElementById('employee_name').value);
            console.log('الدور الوظيفي:', document.getElementById('employee_role').value);
            console.log('النادي:', document.getElementById('club_name').value);
            console.log('رقم المخالفة:', document.getElementById('violation_number').value);

            // ملء الحقول المخفية
            document.getElementById('employee_id_hidden').value = "{{ employee.employee_id }}";
            document.getElementById('employee_name_hidden').value = "{{ employee.name }}";
            document.getElementById('employee_role_hidden').value = "{{ employee.role }}";
            document.getElementById('club_name_hidden').value = "{{ employee.club.name }}";
            document.getElementById('violation_number_hidden').value = "{{ violations_count + 1 }}";
        } catch (e) {
            console.error('خطأ في ملء بيانات الموظف:', e);
        }
        {% endif %}

// تم حذف الكود القديم
    });
</script>
{% endblock %}
