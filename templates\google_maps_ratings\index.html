{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">تقييم النادي - Google Maps</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item active" aria-current="page">تقييم النادي - Google Maps</li>
            </ol>
        </nav>
    </div>
</div>

<!-- شريط البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <input type="text" class="form-control" name="search" value="{{ search_query }}" placeholder="البحث عن نادي...">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i> بحث
                </button>
                {% if search_query %}
                <a href="{{ url_for('google_maps_ratings') }}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i> إلغاء البحث
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<!-- قائمة النوادي مع التقييمات -->
<div class="card shadow-sm">
    <div class="card-header bg-primary text-white py-3">
        <h5 class="mb-0"><i class="fas fa-star me-2"></i> تقييمات Google Maps للأندية</h5>
    </div>
    <div class="card-body">
        {% if clubs_with_ratings %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="25%">اسم النادي</th>
                        <th class="text-center" width="15%">آخر تاريخ تقييم</th>
                        <th class="text-center" width="15%">عدد التقييمات</th>
                        <th class="text-center" width="15%">متوسط التقييم</th>
                        <th class="text-center" width="25%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in clubs_with_ratings %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>
                            <strong>{{ item.club.name }}</strong>
                            {% if item.club.location %}
                            <br><small class="text-muted">{{ item.club.location }}</small>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if item.latest_rating %}
                                {{ item.latest_rating.date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">لا يوجد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if item.latest_rating %}
                                <span class="badge bg-info force-english-numbers">{{ item.latest_rating.rating_count }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if item.latest_rating %}
                                <div class="d-flex align-items-center justify-content-center">
                                    <span class="me-2 force-english-numbers">{{ "%.1f"|format(item.latest_rating.rating_average) }}</span>
                                    <div class="text-warning">
                                        {% for i in range(5) %}
                                            {% if i < item.latest_rating.rating_average %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('google_maps_ratings_club', club_id=item.club.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="عرض التقييمات">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('add_google_maps_rating', club_id=item.club.id) }}" 
                                   class="btn btn-sm btn-outline-success" title="إضافة تقييم">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-star fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أندية للعرض</h5>
            {% if search_query %}
            <p class="text-muted">لم يتم العثور على أندية تطابق البحث "{{ search_query }}"</p>
            <a href="{{ url_for('google_maps_ratings') }}" class="btn btn-primary mt-2">
                <i class="fas fa-arrow-right me-1"></i> عرض جميع الأندية
            </a>
            {% else %}
            <p class="text-muted">لا توجد أندية مسجلة في النظام</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<div class="mt-4">
    <div class="alert alert-info">
        <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i> معلومات حول تقييمات Google Maps:</h5>
        <ul class="mb-0">
            <li>يمكنك متابعة تقييمات النوادي على Google Maps من خلال هذه الصفحة</li>
            <li>يتم عرض آخر تقييم مسجل لكل نادي مع عدد التقييمات ومتوسط النجوم</li>
            <li>انقر على "عرض التقييمات" لرؤية تاريخ جميع التقييمات للنادي</li>
            <li>انقر على "إضافة تقييم" لتسجيل تقييم جديد للنادي</li>
        </ul>
    </div>
</div>
{% endblock %}
