{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mb-1">تفاصيل الموظف</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('employees') }}">الموظفين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ employee.name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if current_user.has_permission('edit_employee') %}
            <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            {% endif %}
            {% if current_user.has_permission('delete_employee') %}
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="fas fa-trash me-1"></i> حذف
            </button>
            {% endif %}
            <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-id-card me-2"></i> معلومات الموظف</h5>
                    <span class="badge bg-light text-primary">{{ employee.employee_id }}</span>
                </div>
                <div class="card-body">
                    <div class="mb-4 text-center">
                        <div class="display-1 text-primary mb-3">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <h3 class="text-primary">{{ employee.name }}</h3>
                        <p class="text-muted">{{ employee.position }}</p>
                    </div>

                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-briefcase text-info me-2"></i>
                                <strong>الدور الوظيفي:</strong>
                            </div>
                            <span>{{ employee.role }}</span>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-building text-success me-2"></i>
                                <strong>النادي:</strong>
                            </div>
                            <a href="{{ url_for('club_detail', id=employee.club_id) }}" class="text-decoration-none">{{ employee.club.name }}</a>
                        </div>



                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-toggle-on text-primary me-2"></i>
                                <strong>الحالة:</strong>
                            </div>
                            {% if employee.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </div>

                        {% if employee.position == 'مدرب' and employee.trainer_type %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-clock text-warning me-2"></i>
                                <strong>حالة المدرب:</strong>
                            </div>
                            {% if employee.trainer_type == 'متفرغ' %}
                            <span class="badge bg-success">متفرغ</span>
                            {% else %}
                            <span class="badge bg-secondary">غير متفرغ</span>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% if current_user.has_permission('edit_employee') %}
                <div class="card-footer">
                    <form action="{{ url_for('toggle_employee', id=employee.id) }}" method="post">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            {% if employee.is_active %}
                            <i class="fas fa-toggle-off me-1"></i> تعطيل الموظف
                            {% else %}
                            <i class="fas fa-toggle-on me-1"></i> تفعيل الموظف
                            {% endif %}
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-address-card me-2"></i> معلومات الاتصال</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-phone text-success me-2"></i> رقم الهاتف</h5>
                                    {% if employee.phone %}
                                    <p class="card-text">
                                        <a href="tel:{{ employee.phone }}" class="text-decoration-none">{{ employee.phone }}</a>
                                    </p>
                                    {% else %}
                                    <p class="card-text text-muted">غير متوفر</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Delete Confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف الموظف "{{ employee.name }}" ({{ employee.employee_id }})؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('delete_employee', id=employee.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
