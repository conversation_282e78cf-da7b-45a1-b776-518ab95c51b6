{% extends "base.html" %}

{% block content %}
<style>
    .icon-box {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .user-info {
        border-right: 4px solid #0d6efd;
    }
    .card-header {
        border-bottom: 0;
    }
    .form-control:focus {
        box-shadow: none;
        border-color: #0d6efd;
    }
</style>
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-id-card me-2"></i>بطاقة المستخدم</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات المستخدم -->
                        <div class="col-md-7 mb-4">
                            <div class="user-info p-3 bg-light rounded h-100">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-info-circle me-2"></i>معلومات المستخدم</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">الاسم</small>
                                                <strong>{{ current_user.name }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-id-badge"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">اسم المستخدم</small>
                                                <strong>{{ current_user.username }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-phone"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">رقم الهاتف</small>
                                                <strong>{{ current_user.phone }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-box bg-primary text-white rounded-circle p-2 me-2">
                                                <i class="fas fa-user-shield"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">نوع الحساب</small>
                                                {% if current_user.role == 'admin' %}
                                                <span class="badge bg-danger">مسؤول النظام</span>
                                                {% elif current_user.role == 'manager' %}
                                                <span class="badge bg-warning text-dark">مدير</span>
                                                {% elif current_user.role == 'supervisor' %}
                                                <span class="badge bg-info">مشرف</span>
                                                {% else %}
                                                <span class="badge bg-success">مستخدم عادي</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% if current_user.clubs %}
                                <div class="mt-4">
                                    <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-building me-2"></i>النوادي المرتبطة</h5>
                                    <div class="row">
                                        {% for club in current_user.clubs %}
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center bg-info bg-opacity-10 rounded p-2">
                                                <i class="fas fa-building me-2 text-primary"></i>
                                                <span>{{ club.name }}</span>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- تغيير كلمة المرور -->
                        <div class="col-md-5 mb-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-key me-2"></i>تغيير كلمة المرور</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="{{ url_for('user_profile') }}">
                                        <div class="mb-3">
                                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-check-double"></i></span>
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                            </div>
                                        </div>
                                        <div class="d-grid mt-4">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
