{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">سجل التشيكات</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('new_check') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> تشيك جديد
        </a>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <form method="get" action="{{ url_for('checks_list') }}" class="row g-2 align-items-center" id="checks-filter-form">
                    <div class="col-md-6">
                        <input type="text" name="search" class="form-control form-control-sm" placeholder="البحث عن تشيك..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-4" id="month-select-container" style="display: none;">
                        <select name="month" class="form-select form-select-sm">
                            {% set current_month = current_month or now().month %}
                            {% set current_year = current_year or now().year %}
                            {% for m in range(1, 13) %}
                                <option value="{{ m }}" {% if month_filter|default(current_month) == m %}selected{% endif %}>
                                    {{ "{:02d}".format(m) }} / {{ current_year }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex gap-1">
                        <button type="submit" class="btn btn-sm btn-primary w-100">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" id="show-month-filter" onclick="document.getElementById('month-select-container').style.display='block'; this.style.display='none';">
                            تصفية حسب شهر آخر
                        </button>
                    </div>
                </form>
                <script>
                    window.addEventListener('DOMContentLoaded', function() {
                        var urlParams = new URLSearchParams(window.location.search);
                        if (urlParams.has('month')) {
                            document.getElementById('month-select-container').style.display = 'block';
                            var btn = document.getElementById('show-month-filter');
                            if (btn) btn.style.display = 'none';
                        }
                    });
                </script>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <i class="fas fa-clipboard-check me-1"></i> قائمة التشيكات
            </div>
            <div class="card-body p-0">
                {% if checks %}
                <div class="table-responsive">
                    <table class="table table-sm mb-0">
                        <thead>
                            <tr>
                                <th class="text-center">#</th>
                                <th class="text-center">التاريخ</th>
                                <th class="text-center">النادي</th>
                                <!-- تم إخفاء عمود عدد التشيكات مؤقتاً -->
                                <!-- <th class="text-center">عدد التشيكات</th> -->
                                <th class="text-center">النسبة</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in checks %}
                            <tr>
                                <td class="text-center py-2">{{ check.id }}</td>
                                <td class="text-center py-2">{{ check.check_date.strftime('%Y-%m-%d') }}</td>
                                <td class="text-center py-2">{{ check.club.name }}</td>
                                <!-- تم إخفاء خلية عدد التشيكات مؤقتاً -->
                                <!-- <td class="text-center py-2">{{ check.items|length }}</td> -->
                                <td class="text-center py-2">
                                    {% set compliance_percentage = check.get_compliance_percentage(month=month_filter|default(current_month), year=current_year) %}
                                    <div class="progress" style="height: 25px; background-color: #e9ecef; position: relative;">
                                        <div class="progress-bar {% if compliance_percentage < 50 %}bg-danger{% elif compliance_percentage < 80 %}bg-warning{% else %}bg-success{% endif %}" role="progressbar" style="width: {{ compliance_percentage }}%; min-width: 2em;"></div>
                                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center;">
                                            <span style="color: white; font-weight: bold; font-size: 18px; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">{{ compliance_percentage }}%</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center py-2">
                                    <a href="{{ url_for('check_detail', id=check.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('edit_check', id=check.id) }}" class="btn btn-sm btn-warning py-1 px-2" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('delete_check', id=check.id) }}" class="btn btn-sm btn-danger py-1 px-2" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info py-2 mb-0">
                    لا توجد تشيكات مسجلة حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
