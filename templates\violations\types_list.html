{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">أنواع المخالفات</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_clubs_list') }}">سجل المخالفات</a></li>
                <li class="breadcrumb-item active" aria-current="page">أنواع المخالفات</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('add_violation_type') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة نوع مخالفة جديد
        </a>
        <a href="{{ url_for('import_violation_types') }}" class="btn btn-success ms-2">
            <i class="fas fa-file-import me-1"></i> استيراد من ملف إكسل
        </a>
        <a href="{{ url_for('violations_clubs_list') }}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light py-3">
        <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> قائمة أنواع المخالفات</h5>
    </div>
    <div class="card-body">
        {% if violation_types %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th width="25%">اسم نوع المخالفة</th>
                        <th width="40%">الوصف</th>
                        <th class="text-center" width="10%">الحالة</th>
                        <th class="text-center" width="10%">مستورد</th>
                        <th class="text-center" width="10%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for type in violation_types %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>{{ type.name }}</td>
                        <td>{{ type.description }}</td>
                        <td class="text-center">
                            {% if type.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if type.is_imported %}
                            <span class="badge bg-info">نعم</span>
                            {% else %}
                            <span class="badge bg-secondary">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('view_violation_type', id=type.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_violation_type', id=type.id) }}" class="btn btn-sm btn-warning py-1 px-2" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if type.violations_count == 0 %}
                                <form action="{{ url_for('delete_violation_type', id=type.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف نوع المخالفة؟');">
                                    <button type="submit" class="btn btn-sm btn-danger py-1 px-2" title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أنواع مخالفات للعرض</h5>
            <p class="text-muted">قم بإضافة نوع مخالفة جديد أو استيراد من ملف إكسل للبدء</p>
            <div class="mt-3">
                <a href="{{ url_for('add_violation_type') }}" class="btn btn-primary me-2">
                    <i class="fas fa-plus-circle me-1"></i> إضافة نوع مخالفة جديد
                </a>
                <a href="{{ url_for('import_violation_types') }}" class="btn btn-success">
                    <i class="fas fa-file-import me-1"></i> استيراد من ملف إكسل
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
