{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">تعديل تقييم Google Maps - {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings') }}">تقييم النادي - Google Maps</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}">{{ club.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">تعديل تقييم</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تقييمات النادي
        </a>
    </div>
</div>

<!-- معلومات النادي -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> معلومات النادي</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>اسم النادي:</strong> {{ club.name }}</p>
                {% if club.location %}
                <p><strong>الموقع:</strong> {{ club.location }}</p>
                {% endif %}
            </div>
            <div class="col-md-6">
                {% if club.manager_name %}
                <p><strong>مدير النادي:</strong> {{ club.manager_name }}</p>
                {% endif %}
                {% if club.phone %}
                <p><strong>رقم الهاتف:</strong> {{ club.phone }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل التقييم -->
<div class="card shadow-sm">
    <div class="card-header bg-warning text-dark py-3">
        <h5 class="mb-0"><i class="fas fa-edit me-2"></i> تعديل تقييم Google Maps</h5>
    </div>
    <div class="card-body">
        <form method="POST" novalidate>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">
                            <i class="fas fa-calendar me-1"></i> التاريخ <span class="text-danger">*</span>
                        </label>
                        <input type="date" class="form-control" id="date" name="date" 
                               value="{{ rating.date.strftime('%Y-%m-%d') }}" required>
                        <div class="form-text">تاريخ تسجيل التقييم</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_count" class="form-label">
                            <i class="fas fa-users me-1"></i> عدد التقييمات <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers" id="rating_count" name="rating_count"
                               min="0" step="1" value="{{ rating.rating_count }}" required placeholder="مثال: 150">
                        <div class="form-text">العدد الإجمالي للأشخاص الذين قيموا النادي</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="total_points" class="form-label">
                            <i class="fas fa-calculator me-1"></i> إجمالي النقاط <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers" id="total_points" name="total_points"
                               min="0" step="0.1" value="{{ (rating.rating_count * rating.rating_average)|round(1) }}" required placeholder="مثال: 630">
                        <div class="form-text">مجموع النقاط من Google Maps (النقاط الإجمالية للتقييمات)</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_average" class="form-label">
                            <i class="fas fa-star me-1"></i> متوسط التقييم <span class="text-muted">(محسوب تلقائياً)</span>
                        </label>
                        <input type="number" class="form-control english-number force-english-numbers bg-light" id="rating_average" name="rating_average"
                               min="0" max="5" step="0.1" value="{{ rating.rating_average }}" readonly placeholder="سيتم الحساب تلقائياً">
                        <div class="form-text">متوسط التقييم (إجمالي النقاط ÷ عدد التقييمات)</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">معاينة التقييم</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="rating-preview" class="d-flex align-items-center">
                                <span id="rating-value" class="me-2 fw-bold force-english-numbers">{{ "%.1f"|format(rating.rating_average) }}</span>
                                <div id="rating-stars" class="text-warning">
                                    {% for i in range(5) %}
                                        {% if i < rating.rating_average %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التقييم الحالي -->
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-light border">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i> معلومات التقييم الحالي:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <small><strong>تاريخ الإضافة:</strong> {{ rating.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <div class="col-md-4">
                                {% if rating.updated_at != rating.created_at %}
                                <small><strong>آخر تحديث:</strong> {{ rating.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                {% else %}
                                <small class="text-muted">لم يتم التحديث بعد</small>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <small><strong>معرف التقييم:</strong> #{{ rating.id }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i> حفظ التعديلات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="mt-4">
    <div class="alert alert-warning">
        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i> تنبيه:</h5>
        <ul class="mb-0">
            <li>تأكد من صحة البيانات قبل الحفظ</li>
            <li>عدد التقييمات يجب أن يكون رقماً صحيحاً (مثال: 150)</li>
            <li>عدّل إجمالي النقاط وسيتم حساب متوسط التقييم تلقائياً</li>
            <li>مثال: إذا كان لديك 630 نقطة و 150 تقييم، فإن المتوسط = 630 ÷ 150 = 4.2</li>
            <li>لا يمكن تعديل التاريخ إلى تاريخ يحتوي على تقييم آخر لنفس النادي</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تعديل التقييم');

    // عناصر الحساب
    const ratingCountInput = document.getElementById('rating_count');
    const totalPointsInput = document.getElementById('total_points');
    const ratingAverageInput = document.getElementById('rating_average');
    const ratingValue = document.getElementById('rating-value');
    const ratingStars = document.getElementById('rating-stars');

    // التحقق من وجود العناصر
    if (!ratingCountInput || !totalPointsInput || !ratingAverageInput) {
        console.error('لم يتم العثور على عناصر الحساب');
        return;
    }

    console.log('تم العثور على جميع العناصر');

    // حساب متوسط التقييم تلقائياً
    function calculateRatingAverage() {
        console.log('بدء حساب متوسط التقييم');

        const ratingCount = parseFloat(ratingCountInput.value) || 0;
        const totalPoints = parseFloat(totalPointsInput.value) || 0;

        console.log(`عدد التقييمات: ${ratingCount}, إجمالي النقاط: ${totalPoints}`);

        if (ratingCount > 0 && totalPoints > 0) {
            const average = totalPoints / ratingCount;
            const roundedAverage = Math.round(average * 10) / 10; // تقريب لرقم عشري واحد

            // التأكد من أن المتوسط لا يتجاوز 5
            const finalAverage = Math.min(roundedAverage, 5.0);

            console.log(`المتوسط المحسوب: ${finalAverage}`);

            ratingAverageInput.value = finalAverage;

            // تحديث معاينة التقييم
            updateRatingPreview(finalAverage);
        } else {
            console.log('قيم غير صالحة للحساب');
            ratingAverageInput.value = '';
            updateRatingPreview(0);
        }
    }

    // معاينة التقييم
    function updateRatingPreview(rating = null) {
        if (!ratingValue || !ratingStars) {
            console.error('عناصر المعاينة غير موجودة');
            return;
        }

        if (rating === null) {
            rating = parseFloat(ratingAverageInput.value) || 0;
        }

        ratingValue.textContent = rating.toFixed(1);

        let starsHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHtml += '<i class="fas fa-star"></i>';
            } else if (i - 0.5 <= rating) {
                starsHtml += '<i class="fas fa-star-half-alt"></i>';
            } else {
                starsHtml += '<i class="far fa-star"></i>';
            }
        }
        ratingStars.innerHTML = starsHtml;

        console.log(`تم تحديث المعاينة: ${rating.toFixed(1)} نجوم`);
    }

    // إضافة مستمعي الأحداث
    ratingCountInput.addEventListener('input', function() {
        console.log('تغيير في عدد التقييمات:', this.value);
        calculateRatingAverage();
    });

    totalPointsInput.addEventListener('input', function() {
        console.log('تغيير في إجمالي النقاط:', this.value);
        calculateRatingAverage();
    });

    // تحديث أولي
    updateRatingPreview();
    calculateRatingAverage(); // حساب متوسط التقييم عند التحميل

    // إضافة تلميحات مفيدة
    totalPointsInput.addEventListener('focus', function() {
        const ratingCount = parseFloat(ratingCountInput.value) || 0;
        if (ratingCount > 0) {
            this.placeholder = `مثال: للحصول على متوسط 4.2، أدخل ${(ratingCount * 4.2).toFixed(1)}`;
        } else {
            this.placeholder = 'أدخل إجمالي النقاط من Google Maps';
        }
    });

    totalPointsInput.addEventListener('blur', function() {
        this.placeholder = 'مثال: 630';
    });

    // اختبار الحساب عند التحميل
    console.log('تم إعداد جميع مستمعي الأحداث');
});
</script>
{% endblock %}
