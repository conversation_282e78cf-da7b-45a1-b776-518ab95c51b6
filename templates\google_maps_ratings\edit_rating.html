{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">تعديل تقييم Google Maps - {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings') }}">تقييم النادي - Google Maps</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}">{{ club.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">تعديل تقييم</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تقييمات النادي
        </a>
    </div>
</div>

<!-- معلومات النادي -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-building me-2"></i> معلومات النادي</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>اسم النادي:</strong> {{ club.name }}</p>
                {% if club.location %}
                <p><strong>الموقع:</strong> {{ club.location }}</p>
                {% endif %}
            </div>
            <div class="col-md-6">
                {% if club.manager_name %}
                <p><strong>مدير النادي:</strong> {{ club.manager_name }}</p>
                {% endif %}
                {% if club.phone %}
                <p><strong>رقم الهاتف:</strong> {{ club.phone }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل التقييم -->
<div class="card shadow-sm">
    <div class="card-header bg-warning text-dark py-3">
        <h5 class="mb-0"><i class="fas fa-edit me-2"></i> تعديل تقييم Google Maps</h5>
    </div>
    <div class="card-body">
        <form method="POST" novalidate>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">
                            <i class="fas fa-calendar me-1"></i> التاريخ <span class="text-danger">*</span>
                        </label>
                        <input type="date" class="form-control" id="date" name="date" 
                               value="{{ rating.date.strftime('%Y-%m-%d') }}" required>
                        <div class="form-text">تاريخ تسجيل التقييم</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_count" class="form-label">
                            <i class="fas fa-users me-1"></i> عدد التقييمات <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number" id="rating_count" name="rating_count" 
                               min="0" step="1" value="{{ rating.rating_count }}" required placeholder="مثال: 150">
                        <div class="form-text">العدد الإجمالي للأشخاص الذين قيموا النادي</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rating_average" class="form-label">
                            <i class="fas fa-star me-1"></i> متوسط التقييم <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control english-number" id="rating_average" name="rating_average" 
                               min="0" max="5" step="0.1" value="{{ rating.rating_average }}" required placeholder="مثال: 4.2">
                        <div class="form-text">متوسط التقييم من 0 إلى 5 نجوم</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">معاينة التقييم</label>
                        <div class="border rounded p-3 bg-light">
                            <div id="rating-preview" class="d-flex align-items-center">
                                <span id="rating-value" class="me-2 fw-bold">{{ "%.1f"|format(rating.rating_average) }}</span>
                                <div id="rating-stars" class="text-warning">
                                    {% for i in range(5) %}
                                        {% if i < rating.rating_average %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التقييم الحالي -->
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-light border">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i> معلومات التقييم الحالي:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <small><strong>تاريخ الإضافة:</strong> {{ rating.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <div class="col-md-4">
                                {% if rating.updated_at != rating.created_at %}
                                <small><strong>آخر تحديث:</strong> {{ rating.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                {% else %}
                                <small class="text-muted">لم يتم التحديث بعد</small>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <small><strong>معرف التقييم:</strong> #{{ rating.id }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('google_maps_ratings_club', club_id=club.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i> حفظ التعديلات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="mt-4">
    <div class="alert alert-warning">
        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i> تنبيه:</h5>
        <ul class="mb-0">
            <li>تأكد من صحة البيانات قبل الحفظ</li>
            <li>عدد التقييمات يجب أن يكون رقماً صحيحاً (مثال: 150)</li>
            <li>متوسط التقييم يجب أن يكون بين 0 و 5 (مثال: 4.2)</li>
            <li>لا يمكن تعديل التاريخ إلى تاريخ يحتوي على تقييم آخر لنفس النادي</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة التقييم
    const ratingInput = document.getElementById('rating_average');
    const ratingValue = document.getElementById('rating-value');
    const ratingStars = document.getElementById('rating-stars');

    function updateRatingPreview() {
        const rating = parseFloat(ratingInput.value) || 0;
        ratingValue.textContent = rating.toFixed(1);
        
        let starsHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                starsHtml += '<i class="fas fa-star"></i>';
            } else if (i - 0.5 <= rating) {
                starsHtml += '<i class="fas fa-star-half-alt"></i>';
            } else {
                starsHtml += '<i class="far fa-star"></i>';
            }
        }
        ratingStars.innerHTML = starsHtml;
    }

    ratingInput.addEventListener('input', updateRatingPreview);
});
</script>
{% endblock %}
