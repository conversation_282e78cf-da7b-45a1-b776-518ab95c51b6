/* تنسيق حقول التاريخ */
input[type="date"] {
    font-family: 'Segoe UI', Arial, sans-serif !important;
    direction: ltr !important;
    height: 38px;
    font-size: 14px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: #fff;
    position: relative;
    -webkit-locale: "en" !important;
    locale: "en" !important;
}

/* تحسين مظهر حقل التاريخ عند التركيز */
input[type="date"]:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين مظهر أيقونة التقويم */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0.8;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23007bff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: contain;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* إزالة الحدود عند النقر في Firefox */
input[type="date"]::-moz-focus-inner {
    border: 0;
}

/* تنسيق حقل التاريخ المعطل */
input[type="date"]:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
    cursor: not-allowed;
}

/* تنسيق حقل التاريخ النشط */
.active-field input[type="date"] {
    border-color: #28a745;
    border-width: 2px;
}

.active-field label {
    color: #28a745;
    font-weight: bold;
}

/* إضافة أيقونة لحقل التاريخ النشط */
.active-field label::after {
    content: " ✓";
    color: #28a745;
}
