{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">قائمة الأندية</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('new_check') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> تشيك جديد
        </a>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <form method="get" action="{{ url_for('checks_clubs_list') }}" class="row g-2">
                    <div class="col-md-10">
                        <input type="text" name="search" class="form-control form-control-sm" placeholder="البحث عن نادي..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-sm btn-primary w-100">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2" >
                <i class="fas fa-building me-1"></i> قائمة الأندية
            </div>
            <div class="card-body p-0">
                {% if clubs %}
                <div class="table-responsive">
                    <table class="table table-sm mb-0">
                        <thead>
                            <tr>
                                <th class="text-center" style="color: rgb(27, 27, 27); background:rgb(243, 246, 119); font-size: 18px; ">#</th>
                                <th class="text-center" style="color: rgb(27, 27, 27); background:rgb(243, 246, 119); font-size: 18px; ">اسم النادي</th>
                                <th class="text-center" style="color: rgb(27, 27, 27); background:rgb(243, 246, 119); font-size: 18px; ">عدد التشيكات</th>
                                <th class="text-center" style="color: rgb(27, 27, 27); background:rgb(243, 246, 119); font-size: 18px; ">تاريخ آخر تشيك</th>
                                <th class="text-center" style="color: rgb(27, 27, 27); background:rgb(243, 246, 119); font-size: 18px; ">متوسط النسبة (الشهر الحالي)</th>
                                <th class="text-center" style="color: rgb(27, 27, 27); background:rgb(243, 246, 119); font-size: 18px; ">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for club in clubs %}
                            <tr>
                                <td class="text-center py-2">{{ loop.index }}</td>
                                <td class="text-center py-2">{{ club.name }}</td>
                                <td class="text-center py-2">{{ club.checks|length }}</td>
                                <td class="text-center py-2">
                                    {% if club.checks %}
                                        {% set latest_check = club.checks|sort(attribute='check_date', reverse=true)|first %}
                                        {{ latest_check.check_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-center py-2">
                                    {% if club.stats.all_count > 0 %}
                                        <div class="progress" style="height: 35px; background-color: #e9ecef; position: relative;">
                                            <div class="progress-bar {% if club.stats.avg_percentage < 50 %}bg-danger{% elif club.stats.avg_percentage < 80 %}bg-warning{% else %}bg-success{% endif %}" role="progressbar" style="width: {{ club.stats.avg_percentage }}%; min-width: 2em;"></div>
                                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center;">
                                                <span style="color: white; font-weight: bold; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">
                                                    {% if club.stats.current_count > 0 %}
                                                        {{ current_month_name }}: {{ club.stats.current_avg_percentage }}%
                                                    {% else %}
                                                        {{ club.stats.avg_percentage }}%
                                                    {% endif %}
                                                </span>
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="progress" style="height: 35px; background-color: #e9ecef; position: relative;">
                                            <div class="progress-bar bg-secondary" role="progressbar" style="width: 100%; min-width: 2em;"></div>
                                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center;">
                                                <span style="color: white; font-weight: bold; font-size: 18px; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">لا يوجد</span>
                                            </div>
                                        </div>
                                    {% endif %}
                                </td>
                                <td class="text-center py-2">
                                    <a href="{{ url_for('checks_club_detail', club_id=club.id) }}" class="btn btn-sm btn-info py-1 px-2" title="عرض تشيكات النادي">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('new_check', club_id=club.id) }}" class="btn btn-sm btn-success py-1 px-2" title="إضافة تشيك جديد">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info py-2 mb-0">
                    لا توجد أندية مسجلة حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
