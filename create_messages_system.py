import sqlite3
import os
from datetime import datetime

def create_messages_system():
    """
    إنشاء نظام المراسلة في قاعدة البيانات
    """
    print("بدء إنشاء نظام المراسلة...")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # إنشاء جدول الرسائل الرئيسي
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS message (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subject VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            sender_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            priority VARCHAR(20) DEFAULT 'normal',
            message_type VARCHAR(50) DEFAULT 'general',
            parent_message_id INTEGER,
            FOREI<PERSON><PERSON> KEY (sender_id) REFERENCES user (id) ON DELETE CASCADE,
            FOREI<PERSON><PERSON> KEY (parent_message_id) REFERENCES message (id) ON DELETE CASCADE
        )
        """)
        print("تم إنشاء جدول message")
        
        # إنشاء جدول المستقبلين
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS message_recipient (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message_id INTEGER NOT NULL,
            recipient_id INTEGER NOT NULL,
            recipient_type VARCHAR(20) DEFAULT 'user',
            is_read BOOLEAN DEFAULT 0,
            read_at DATETIME,
            is_deleted BOOLEAN DEFAULT 0,
            deleted_at DATETIME,
            FOREIGN KEY (message_id) REFERENCES message (id) ON DELETE CASCADE,
            FOREIGN KEY (recipient_id) REFERENCES user (id) ON DELETE CASCADE
        )
        """)
        print("تم إنشاء جدول message_recipient")
        
        # إنشاء جدول المرفقات
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS message_attachment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message_id INTEGER NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INTEGER,
            mime_type VARCHAR(100),
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (message_id) REFERENCES message (id) ON DELETE CASCADE
        )
        """)
        print("تم إنشاء جدول message_attachment")
        
        # إنشاء فهارس لتحسين الأداء
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_message_sender ON message(sender_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_message_created ON message(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_recipient_message ON message_recipient(message_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_recipient_user ON message_recipient(recipient_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_recipient_read ON message_recipient(is_read)")
        print("تم إنشاء الفهارس")
        
        # إضافة صلاحيات نظام المراسلة
        message_permissions = [
            ('إدارة الرسائل - عرض', 'عرض الرسائل', 'view_messages'),
            ('إدارة الرسائل - إرسال', 'إرسال رسالة جديدة', 'send_message'),
            ('إدارة الرسائل - رد', 'الرد على الرسائل', 'reply_message'),
            ('إدارة الرسائل - حذف', 'حذف الرسائل', 'delete_message'),
            ('إدارة الرسائل - إرسال عام', 'إرسال رسالة لجميع المستخدمين', 'send_broadcast_message'),
            ('إدارة الرسائل - إدارة', 'إدارة جميع الرسائل', 'manage_all_messages'),
        ]
        
        for name, description, code in message_permissions:
            # التحقق من وجود الصلاحية
            cursor.execute("SELECT id FROM permission WHERE code = ?", (code,))
            existing = cursor.fetchone()
            
            if not existing:
                cursor.execute(
                    "INSERT INTO permission (name, description, code) VALUES (?, ?, ?)",
                    (name, description, code)
                )
                permission_id = cursor.lastrowid
                print(f"تمت إضافة صلاحية: {name}")
                
                # إضافة الصلاحية للمسؤول
                cursor.execute(
                    "INSERT INTO role_permission (role, permission_id) VALUES (?, ?)",
                    ("admin", permission_id)
                )
                
                # إضافة صلاحيات أساسية للمدير
                if code in ['view_messages', 'send_message', 'reply_message', 'send_broadcast_message']:
                    cursor.execute(
                        "INSERT INTO role_permission (role, permission_id) VALUES (?, ?)",
                        ("manager", permission_id)
                    )
                
                # إضافة صلاحيات أساسية للمشرف
                if code in ['view_messages', 'send_message', 'reply_message']:
                    cursor.execute(
                        "INSERT INTO role_permission (role, permission_id) VALUES (?, ?)",
                        ("supervisor", permission_id)
                    )
                
                # إضافة صلاحيات أساسية للمستخدم العادي
                if code in ['view_messages', 'send_message', 'reply_message']:
                    cursor.execute(
                        "INSERT INTO role_permission (role, permission_id) VALUES (?, ?)",
                        ("user", permission_id)
                    )
        
        # حفظ التغييرات
        conn.commit()
        print("تم إنشاء نظام المراسلة بنجاح!")
        
    except Exception as e:
        print(f"خطأ في إنشاء نظام المراسلة: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_messages_system()
