{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3">تشيك جديد</h1>
    </div>
    <div class="col-md-4 text-start">
        <a href="{{ url_for('checks_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى سجل التشيكات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light py-2">
                <i class="fas fa-clipboard-check me-1"></i> بيانات التشيك
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('new_check') }}" enctype="multipart/form-data" id="checkForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="club_id" class="form-label">النادي</label>
                                <select class="form-select" id="club_id" name="club_id" required>
                                    <option value="" selected disabled>اختر النادي</option>
                                    {% for club in clubs %}
                                    <option value="{{ club.id }}" {% if selected_club and selected_club.id == club.id %}selected{% endif %}>
                                        {{ club.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات عامة</label>
                                <textarea class="form-control" id="notes" name="notes" rows="1"></textarea>
                            </div>
                        </div>
                    </div>

                    <div id="facilitiesContainer">
                        {% if not selected_club %}
                        <!-- هنا سيتم عرض المرافق وبنودها بعد اختيار النادي -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يرجى اختيار النادي لعرض المرافق والبنود الخاصة به.
                        </div>
                        {% else %}
                        <!-- عرض المرافق والبنود الخاصة بالنادي المحدد -->
                        {% if facilities_data|length == 0 %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد مرافق أو بنود نشطة لهذا النادي.
                        </div>
                        {% else %}
                        {% for facility in facilities_data %}
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-building me-2"></i> {{ facility.name }}</h5>
                                <div>
                                    <span class="me-2">عدد البنود: <strong>{{ facility.items_count }}</strong></span>
                                    <span class="me-2">مطابق: <strong class="text-light compliant-count">0</strong></span>
                                    <span>غير مطابق: <strong class="text-light non-compliant-count">{{ facility.items_count }}</strong></span>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 50%; text-align: center;background-color: #0d6efd; color: white;">اسم البند</th>
                                                <th style="width: 15%;background-color: #0d6efd; color: white;" class="text-center">الحالة</th>
                                                <th style="width: 25%; text-align: center;background-color: #0d6efd; color: white;">ملاحظات</th>
                                                <th style="width: 10%; text-align: center; background-color: #0d6efd; color: white;">صورة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% for item in facility.facility_items %}
                                        <tr>
                                            <td>{{ item.name }}</td>
                                            <td class="text-center">
                                                <div class="form-check d-flex justify-content-center">
                                                    <input class="form-check-input compliance-checkbox" style="width: 1.5em; height: 1.5em;"
                                                        type="checkbox" id="item_{{ item.id }}" name="item_{{ item.id }}"
                                                        data-facility-id="{{ facility.id }}" data-item-id="{{ item.id }}"
                                                        onclick="if(this.checked) { this.nextElementSibling.querySelector('.compliance-status').innerHTML = '<span class=\'text-success\'>مطابق</span>'; } else { this.nextElementSibling.querySelector('.compliance-status').innerHTML = '<span class=\'text-danger\'>غير مطابق</span>'; } updateFacilityCounters({{ facility.id }});">
                                                    <label class="form-check-label ms-2" for="item_{{ item.id }}">
                                                        <span class="compliance-status text-danger">غير مطابق</span>
                                                    </label>
                                                </div>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm" id="notes_{{ item.id }}" name="notes_{{ item.id }}" placeholder="ملاحظات...">
                                            </td>
                                            <td class="text-center">
                                                <input type="file" class="d-none" id="image_{{ item.id }}" name="image_{{ item.id }}" accept="image/*">
                                                <label for="image_{{ item.id }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-camera"></i>
                                                </label>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>
                        {% endfor %}
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            {% if not facilities_data %}
                            <i class="fas fa-search me-1"></i> جلب مرافق النادي
                            {% else %}
                            <i class="fas fa-save me-1"></i> حفظ التشيك
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // دالة لتحديث حالة الـ checkbox والعدادات
    function updateCheckboxStatus(checkbox) {
        // تحديث نص حالة المطابقة
        const statusElement = checkbox.nextElementSibling.querySelector('.compliance-status');
        if (checkbox.checked) {
            statusElement.innerHTML = '<span class="text-success">مطابق</span>';
        } else {
            statusElement.innerHTML = '<span class="text-danger">غير مطابق</span>';
        }

        // الحصول على معرف المرفق
        const facilityId = checkbox.getAttribute('data-facility-id');

        // تحديث العدادات فوراً
        updateFacilityCounters(facilityId);
    }

    // دالة لتحديث عدادات المرفق مباشرة
    function updateFacilityCounters(facilityId) {
        // العثور على جميع الـ checkboxes التي تنتمي لهذا المرفق
        const checkboxes = document.querySelectorAll(`.compliance-checkbox[data-facility-id="${facilityId}"]`);

        if (checkboxes.length > 0) {
            // حساب العدادات
            const totalItems = checkboxes.length;
            let compliantItems = 0;

            // عد البنود المطابقة
            checkboxes.forEach(function(checkbox) {
                if (checkbox.checked) {
                    compliantItems++;
                }
            });

            const nonCompliantItems = totalItems - compliantItems;

            // العثور على بطاقة المرفق
            const facilityCard = checkboxes[0].closest('.card');

            // تحديث العدادات في الواجهة
            const compliantCountElement = facilityCard.querySelector('.compliant-count');
            const nonCompliantCountElement = facilityCard.querySelector('.non-compliant-count');

            if (compliantCountElement) compliantCountElement.textContent = compliantItems;
            if (nonCompliantCountElement) nonCompliantCountElement.textContent = nonCompliantItems;

            console.log(`Updated facility ${facilityId}: Total=${totalItems}, Compliant=${compliantItems}, Non-Compliant=${nonCompliantItems}`);
        }
    }

    $(document).ready(function() {
        // تهيئة العدادات لكل المرافق عند تحميل الصفحة
        console.log('Page loaded, initializing counters...');

        // التأكد من تحديث العدادات بعد تحميل الصفحة بالكامل
        function initializeCounters() {
            // جمع كل معرفات المرافق الفريدة
            const facilityIds = [];
            $('.compliance-checkbox').each(function() {
                const facilityId = $(this).data('facility-id');
                if (facilityId && facilityIds.indexOf(facilityId) === -1) {
                    facilityIds.push(facilityId);
                }
            });

            if (facilityIds.length === 0) {
                console.warn('No facility IDs found, retrying in 200ms...');
                setTimeout(initializeCounters, 200);
                return;
            }

            // تحديث العدادات لكل مرفق
            facilityIds.forEach(function(facilityId) {
                updateFacilityCounters(facilityId);
            });

            console.log('Successfully initialized counters for facilities:', facilityIds);
        }

        // بدء التهيئة بعد تحميل الصفحة
        setTimeout(initializeCounters, 300);






        // منع إرسال النموذج إذا لم يتم اختيار نادي
        $('#checkForm').submit(function(e) {
            console.log('Form submission attempted');
            const clubId = $('#club_id').val();
            console.log(`Club ID at submission: ${clubId}`);
            if (!clubId) {
                e.preventDefault();
                console.error('No club selected');
                alert('يرجى اختيار النادي أولاً');
                return false;
            }

            // التحقق من وجود مرافق وبنود
            const checkboxCount = $('.compliance-checkbox').length;
            console.log(`Found ${checkboxCount} checkboxes`);
            if (checkboxCount === 0) {
                e.preventDefault();
                console.error('No checkboxes found');
                alert('لا توجد بنود للتشيك. يرجى التأكد من وجود مرافق وبنود نشطة للنادي المحدد.');
                return false;
            }

            console.log('Form submission allowed');
            return true;
        });
    });
</script>
{% endblock %}
