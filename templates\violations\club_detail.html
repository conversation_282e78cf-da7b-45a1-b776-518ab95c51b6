{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-1">موظفي {{ club.name }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('violations_clubs_list') }}">سجل المخالفات</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ club.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        {% if current_user.has_permission('add_violation') %}
        <a href="{{ url_for('new_violation') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة مخالفة جديدة
        </a>
        {% endif %}
        <a href="{{ url_for('violations_clubs_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<!-- معلومات النادي والإحصائيات -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    {{ club.name }}
                </h5>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-users me-1"></i>
                        الموظفين: {{ employees|length }}
                    </span>
                    <span class="badge bg-warning text-dark">
                        <i class="fas fa-calendar-alt me-1"></i>
                        {{ current_month_name }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-light py-3">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i> قائمة الموظفين</h5>
            </div>
            <div class="col-md-4">
                <form action="{{ url_for('violations_club_detail', club_id=club.id) }}" method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{{ url_for('violations_club_detail', club_id=club.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if employees %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="5%">#</th>
                        <th class="text-center" width="15%">الرقم الوظيفي</th>
                        <th width="25%">اسم الموظف</th>
                        <th width="20%">الدور الوظيفي</th>
                        <th class="text-center" width="10%">إجمالي المخالفات</th>
                        <th class="text-center" width="10%">{{ current_month_name }}</th>
                        <th class="text-center" width="15%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td class="text-center">
                            <span class="badge bg-secondary">{{ employee.employee_id }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user text-primary me-2"></i>
                                <strong>{{ employee.name }}</strong>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ employee.role }}</span>
                        </td>
                        <td class="text-center">
                            {% if employee.total_violations > 0 %}
                                <span class="badge bg-warning text-dark">{{ employee.total_violations }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if employee.violations_count > 0 %}
                                <span class="badge bg-danger">{{ employee.violations_count }}</span>
                            {% else %}
                                <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('violations_employee_detail', employee_id=employee.id) }}" class="btn btn-sm btn-primary" title="عرض مخالفات الموظف">
                                    <i class="fas fa-list-alt me-1"></i> عرض المخالفات
                                </a>
                                {% if current_user.has_permission('add_violation') %}
                                <a href="{{ url_for('new_violation_for_employee', employee_id=employee.id) }}" class="btn btn-sm btn-success" title="إضافة مخالفة جديدة">
                                    <i class="fas fa-plus"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد موظفين للعرض</h5>
            <p class="text-muted">قم بإضافة موظف جديد للبدء</p>
            <a href="{{ url_for('new_employee') }}" class="btn btn-primary mt-2">
                <i class="fas fa-plus-circle me-1"></i> إضافة موظف جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block head %}
<style>
    /* تحسين مظهر بطاقة النادي */
    .card-header.bg-primary {
        background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
        border-bottom: 3px solid rgba(255, 255, 255, 0.2);
    }

    /* تحسين مظهر الشارات */
    .badge {
        font-size: 0.75rem;
        padding: 0.4em 0.6em;
        border-radius: 0.375rem;
    }

    .badge.bg-light {
        border: 1px solid #dee2e6;
    }

    /* تحسين مظهر الجداول */
    .table th {
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    /* تحسين مظهر الأزرار */
    .btn-group .btn {
        border-radius: 0.25rem;
        margin: 0 1px;
    }

    /* تحسين مظهر البطاقات */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تحسين مظهر الصفوف */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر الشارات في الرأس */
    .card-header .badge {
        font-size: 0.7rem;
        font-weight: 500;
    }

    /* تحسين المسافات */
    .gap-2 {
        gap: 0.5rem !important;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    function registerViolation(employeeId, employeeNumber, employeeName, employeeRole, clubName) {
        // إنشاء نموذج مؤقت
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("new_violation") }}';
        form.style.display = 'none';

        // إضافة حقول النموذج
        const fields = {
            'employee_id': employeeNumber,
            'employee_id_hidden': employeeNumber,
            'employee_name_hidden': employeeName,
            'employee_role_hidden': employeeRole,
            'club_name_hidden': clubName,
            'auto_fill_js': 'true',
            'employee_data_js': JSON.stringify({
                id: employeeId,
                employee_id: employeeNumber,
                name: employeeName,
                role: employeeRole,
                club_name: clubName
            })
        };

        // إضافة الحقول إلى النموذج
        for (const key in fields) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = fields[key];
            form.appendChild(input);
        }

        // إضافة النموذج إلى الصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
</script>
{% endblock %}
