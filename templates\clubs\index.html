{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>النوادي</h1>
    <div>
        {% if current_user.has_permission('import_clubs_excel') %}
        <a href="{{ url_for('import_clubs_excel') }}" class="btn btn-success me-2">
            <i class="fas fa-file-excel"></i> استيراد من إكسل
        </a>
        {% endif %}
        {% if current_user.has_permission('add_club') %}
        <a href="{{ url_for('new_club') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة نادي جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <form action="{{ url_for('clubs') }}" method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="ابحث عن نادي..." name="search" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                {% if search_query %}
                <a href="{{ url_for('clubs') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> مسح البحث
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if clubs %}
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" width="3%">#</th>
                        <th width="20%">اسم النادي</th>
                        <th width="15%">مدير النادي</th>
                        <th width="12%">رقم الهاتف</th>
                        <th class="text-center" width="12%">عدد الموظفين</th>
                        <th class="text-center" width="12%">عدد المدربين</th>
                        <th class="text-center" width="12%">عدد العمال</th>
                        <th class="text-center" width="14%">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for club in clubs %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>
                            <a href="{{ url_for('club_detail', id=club.id) }}" class="text-decoration-none fw-bold text-primary">
                                {{ club.name }}
                            </a>
                        </td>
                        <td>{{ club.manager_name or 'غير محدد' }}</td>
                        <td>
                            {% if club.phone %}
                            <a href="tel:{{ club.phone }}" class="text-decoration-none">
                                <i class="fas fa-phone-alt text-success me-1"></i> {{ club.phone }}
                            </a>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <!-- عدد الموظفين (خدمة العملاء) -->
                        <td class="text-center employee-status">
                            {% set customer_service_status = club.get_employee_status('customer_service') %}
                            <span class="text-dark fw-bold">{{ customer_service_status.target or 0 }}</span>
                            <span class="text-muted"> / </span>
                            <span class="{{ customer_service_status.color_class }} fw-bold">{{ customer_service_status.count }}</span>
                            <small class="{{ customer_service_status.color_class }} ms-1">{{ customer_service_status.status }}</small>
                        </td>
                        <!-- عدد المدربين -->
                        <td class="text-center employee-status">
                            {% set trainer_status = club.get_employee_status('trainer') %}
                            <span class="text-dark fw-bold">{{ trainer_status.target or 0 }}</span>
                            <span class="text-muted"> / </span>
                            <span class="{{ trainer_status.color_class }} fw-bold">{{ trainer_status.count }}</span>
                            <small class="{{ trainer_status.color_class }} ms-1">{{ trainer_status.status }}</small>
                        </td>
                        <!-- عدد العمال -->
                        <td class="text-center employee-status">
                            {% set worker_status = club.get_employee_status('worker') %}
                            <span class="text-dark fw-bold">{{ worker_status.target or 0 }}</span>
                            <span class="text-muted"> / </span>
                            <span class="{{ worker_status.color_class }} fw-bold">{{ worker_status.count }}</span>
                            <small class="{{ worker_status.color_class }} ms-1">{{ worker_status.status }}</small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('club_detail', id=club.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_permission('edit_club') %}
                                <a href="{{ url_for('edit_club', id=club.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('delete_club') %}
                                <a href="{{ url_for('delete_club', id=club.id) }}" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف النادي \'{{ club.name }}\' ؟'){% if club.employees %} && alert('لا يمكن حذف النادي لأنه يحتوي على موظفين') && false{% endif %}">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-center">لا توجد نوادي حالياً</p>
        {% endif %}
    </div>
</div>


{% endblock %}
