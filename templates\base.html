<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}نظام إدارة مدير المنطقة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">



    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    {% block head %}{% endblock %}
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            font-size: 0.9rem; /* تصغير حجم الخط الافتراضي في كل التطبيق */
        }

        /* فئة خاصة للعناصر التي تحتاج إلى عرض باللغة الإنجليزية */
        .english-input {
            direction: ltr !important;
            text-align: left !important;
            font-family: 'Arial', sans-serif !important;
            unicode-bidi: bidi-override !important;
        }

        /* فئة للأرقام المركزة باللغة الإنجليزية */
        .english-number {
            direction: ltr !important;
            text-align: center !important;
            font-family: 'Arial', sans-serif !important;
            unicode-bidi: bidi-override !important;
        }

        /* فئة عامة لضمان عرض الأرقام بالإنجليزية */
        .force-english-numbers {
            font-family: 'Arial', 'Helvetica', sans-serif !important;
            direction: ltr !important;
            unicode-bidi: bidi-override !important;
        }

        /* تطبيق الأرقام الإنجليزية على جميع حقول الأرقام */
        input[type="number"],
        input[type="tel"],
        .numeric-field,
        .rating-display,
        .count-display {
            font-family: 'Arial', 'Helvetica', sans-serif !important;
            direction: ltr !important;
            unicode-bidi: bidi-override !important;
        }
        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1030;
            padding: 0.3rem 1rem;
            min-height: 50px;
        }
        .card {
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
            border: none;
        }
        .card-header {
            border-radius: 12px 12px 0 0;
            font-weight: 600;
            border-bottom: none;
        }
        .bg-primary {
            color: white !important;
        }
        .bg-primary h1, .bg-primary h2, .bg-primary h3, .bg-primary h4, .bg-primary h5, .bg-primary h6 {
            color: white !important;
        }
        .card .display-4 {
            font-weight: 700;
            margin: 15px 0;
        }
        .btn {
            font-weight: 600;
            border-radius: 6px;
            padding: 6px 12px;
            transition: all 0.3s;
            letter-spacing: 0;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
            font-size: 0.85rem; /* تصغير حجم خط الأزرار */
        }
        .btn-primary {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }
        .table {
            font-size: 0.85rem; /* تصغير حجم الخط في الجداول */
        }
        .table th {
            font-weight: 600;
            color: #4b5563;
            padding: 0.5rem; /* تقليل التباعد الداخلي */
        }
        .table td {
            vertical-align: middle;
            padding: 0.5rem; /* تقليل التباعد الداخلي */
        }
        .table .btn {
            padding: 0.25rem 0.5rem; /* تصغير الأزرار داخل الجداول */
            font-size: 0.8rem;
        }
        .form-control, .form-select {
            border-radius: 6px;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            font-size: 0.9rem; /* تصغير حجم الخط في حقول الإدخال */
        }
        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
        }
        .form-label {
            font-weight: 500;
            color: #4b5563;
        }
        .nav-link {
            font-weight: 500;
            color: rgba(255, 255, 255, 1) !important; /* لون أبيض واضح */
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* ظل خفيف للنص */
        }
        .nav-link.active {
            font-weight: 600;
            color: rgba(255, 255, 255, 1) !important; /* لون أبيض واضح */
        }
        .page-link {
            border-radius: 6px;
            margin: 0 2px;
        }
        .alert {
            border-radius: 10px;
        }
        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            color: #1f2937;
        }
        h1 { font-size: 1.8rem; }
        h2 { font-size: 1.6rem; }
        h3 { font-size: 1.4rem; }
        h4 { font-size: 1.2rem; }
        h5 { font-size: 1.1rem; }
        h6 { font-size: 1rem; }
        /* أنماط الأزرار التفاعلية */
        .toggle-btn {
            transition: all 0.3s ease;
            min-width: 80px;
            position: relative;
            overflow: hidden;
            font-weight: 600;
            padding: 6px 10px;
            border-width: 1px;
            font-size: 0.8rem;
        }
        .toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .toggle-btn:active {
            transform: translateY(0);
        }
        .btn-outline-success.active {
            background-color: #198754;
            color: white;
            box-shadow: 0 2px 5px rgba(25, 135, 84, 0.3);
        }
        .btn-outline-danger {
            color: #dc3545;
            font-weight: 700;
        }
        .btn-outline-success.active:hover {
            background-color: #157347;
        }
        .btn-outline-danger:hover {
            background-color: #dc3545;
            color: white;
        }
        .modal-dialog-centered {
            display: flex;
            align-items: center;
            min-height: calc(100% - 3.5rem);
        }
        .modal {
            position: fixed;
            z-index: 1050;
        }
        .modal-backdrop {
            z-index: 1040;
        }
        .modal-open .modal {
            display: block;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* أنماط شريط التنقل المحسنة */
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 1) !important;
            transition: all 0.3s ease;
            padding: 6px 10px;
            margin: 0 2px;
            font-size: 0.9rem; /* تصغير حجم الخط في شريط التنقل */
        }
        .navbar-dark .navbar-nav .nav-link:hover {
            color: rgba(255, 255, 255, 0.9) !important;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            padding: 0.25rem;
        }
        .dropdown-item {
            font-size: 0.85rem; /* تصغير حجم الخط في القوائم المنسدلة */
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
        }

        /* إزالة الأسهم من حقول الأرقام */
        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* تحسين عرض النص في الأعمدة */
        .employee-status {
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary px-2 fixed-top">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}" style="font-size: 0.9rem;">
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('clubs') }}" style="font-size: 0.9rem;">
                            الأندية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('facilities') }}" style="font-size: 0.9rem;">
                            المرافق
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('employees') }}" style="font-size: 0.9rem;">
                            الموظفين
                        </a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="checksDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            التشيك
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('new_check') }}">
                                <i class="fas fa-plus-circle me-1"></i> تشيك جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('checks_list') }}">
                                <i class="fas fa-list me-1"></i> سجل التشيكات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('check_reports') }}">
                                <i class="fas fa-chart-bar me-1"></i> تقارير التشيك
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="cameraDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            متابعة الكاميرات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('new_camera_check') }}">
                                <i class="fas fa-plus-circle me-1"></i> جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('camera_checks_list') }}">
                                <i class="fas fa-list me-1"></i> سجل المتابعة
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="criticalIssuesDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            الأعطال الحرجة
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('new_critical_issue') }}">
                                <i class="fas fa-plus-circle me-1"></i> إضافة عطل جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('critical_issues_list') }}">
                                <i class="fas fa-list me-1"></i> سجل الأعطال
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="salesDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            المبيعات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('new_sales_target') }}">
                                <i class="fas fa-bullseye me-1"></i> تسجيل تارجيت شهري
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('new_daily_sales') }}">
                                <i class="fas fa-calendar-plus me-1"></i> إضافة مبيعات يومية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('sales_list') }}">
                                <i class="fas fa-list me-1"></i> سجل المبيعات
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="violationsDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            الإجراءات النظامية
                        </a>
                        <ul class="dropdown-menu">
                            <!-- تم إخفاء زر تسجيل مخالفة جديدة مؤقتاً -->
                            <li><a class="dropdown-item" href="{{ url_for('violations_list') }}">
                                <i class="fas fa-list me-1"></i> سجل المخالفات
                            </a></li>
                            {% if current_user.has_permission('import_violation_types') %}
                            <li><a class="dropdown-item" href="{{ url_for('violation_types_list') }}">
                                <i class="fas fa-list-alt me-1"></i> أنواع المخالفات
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="shumoosDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            شموس
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('new_shumoos') }}">
                                <i class="fas fa-plus-circle me-1"></i> إضافة سجل جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('shumoos_list') }}">
                                <i class="fas fa-list me-1"></i> سجل شموس
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('google_maps_ratings') }}" style="font-size: 0.9rem;">
                            تقييم النادي - Google Maps
                        </a>
                    </li>

                    {% if current_user.has_permission('view_messages') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="messagesDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            <i class="fas fa-envelope me-1"></i> الرسائل
                            <span id="unread-messages-badge" class="badge bg-danger ms-1" style="display: none;">0</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('messages_inbox') }}">
                                <i class="fas fa-inbox me-1"></i> صندوق الوارد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages_sent') }}">
                                <i class="fas fa-paper-plane me-1"></i> الرسائل المرسلة
                            </a></li>
                            {% if current_user.has_permission('send_message') %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('compose_message') }}">
                                <i class="fas fa-edit me-1"></i> رسالة جديدة
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" style="font-size: 0.9rem;">
                            {{ current_user.name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% if current_user.is_admin %}
                            <li><a class="dropdown-item" href="{{ url_for('users') }}">
                                <i class="fas fa-user-shield me-1"></i> المستخدمين
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{{ url_for('user_profile') }}">
                                <i class="fas fa-id-card me-1"></i> بطاقة المستخدم
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}" style="font-size: 0.9rem;">
                            تسجيل الدخول
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <main class="container-fluid px-3" style="margin-top: 65px;">
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-info alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2025 نظام إدارة مدير المنطقة</span>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // إخفاء رسائل التنبيه تلقائيًا بعد 5 ثوانٍ
        $(document).ready(function() {
            // استهداف رسائل التنبيه من Flask
            setTimeout(function() {
                $('.alert-dismissible').fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000); // 5000 مللي ثانية = 5 ثوانٍ

            // تحديث عدد الرسائل غير المقروءة
            {% if current_user.is_authenticated and current_user.has_permission('view_messages') %}
            function updateUnreadMessagesCount() {
                $.ajax({
                    url: '{{ url_for("get_unread_count") }}',
                    method: 'GET',
                    success: function(response) {
                        const badge = $('#unread-messages-badge');
                        if (response.count > 0) {
                            badge.text(response.count).show();
                            badge.attr('title', `لديك ${response.count} رسالة غير مقروءة`);
                        } else {
                            badge.hide();
                        }
                    },
                    error: function() {
                        console.log('خطأ في تحديث عدد الرسائل غير المقروءة');
                    }
                });
            }

            // تحديث العدد عند تحميل الصفحة
            updateUnreadMessagesCount();

            // تحديث العدد كل 30 ثانية
            setInterval(updateUnreadMessagesCount, 30000);
            {% endif %}
        });
    </script>


    <script>
        // كود جافاسكريبت للتعامل مع طلبات AJAX
        $(document).ready(function() {
            // تفعيل/تعطيل المرفق
            $(document).on('click', '.toggle-facility-btn', function() {
                const btn = $(this);
                const facilityId = btn.data('facility-id');
                const currentStatus = btn.data('status');

                // إظهار تأثير التحميل
                btn.prop('disabled', true);
                btn.html('<i class="fas fa-spinner fa-spin"></i>');

                // إرسال طلب AJAX
                $.ajax({
                    url: `/api/facilities/${facilityId}/toggle`,
                    method: 'POST',
                    success: function(response) {
                        if (response.success) {
                            // تحديث حالة الزر
                            if (response.is_active) {
                                btn.removeClass('btn-outline-danger').addClass('btn-outline-success active');
                                btn.html('<i class="fas fa-toggle-on me-1"></i><span>نشط</span>');
                                btn.data('status', 'active');
                                btn.attr('title', 'تعطيل');

                                // تحديث شارة الحالة في الجدول
                                btn.closest('tr').find('td:nth-child(3) .badge')
                                   .removeClass('bg-danger').addClass('bg-success')
                                   .text('نشط');
                            } else {
                                btn.removeClass('btn-outline-success active').addClass('btn-outline-danger');
                                btn.html('<i class="fas fa-toggle-off me-1"></i><span>معطل</span>');
                                btn.data('status', 'inactive');
                                btn.attr('title', 'تفعيل');

                                // تحديث شارة الحالة في الجدول
                                btn.closest('tr').find('td:nth-child(3) .badge')
                                   .removeClass('bg-success').addClass('bg-danger')
                                   .text('معطل');
                            }

                            // إظهار رسالة نجاح
                            showAlert(response.message, 'success');
                        }
                    },
                    error: function() {
                        showAlert('حدث خطأ أثناء تنفيذ العملية', 'danger');
                        // إعادة الزر إلى حالته السابقة
                        updateButtonState(btn, currentStatus === 'active');
                    },
                    complete: function() {
                        // إلغاء تأثير التحميل
                        btn.prop('disabled', false);
                    }
                });
            });

            // تفعيل/تعطيل بند المرفق
            $(document).on('click', '.toggle-item-btn', function() {
                const btn = $(this);
                const facilityId = btn.data('facility-id');
                const itemId = btn.data('item-id');
                const currentStatus = btn.data('status');

                // إظهار تأثير التحميل
                btn.prop('disabled', true);
                btn.html('<i class="fas fa-spinner fa-spin"></i>');

                // إرسال طلب AJAX
                $.ajax({
                    url: `/api/facilities/${facilityId}/items/${itemId}/toggle`,
                    method: 'POST',
                    success: function(response) {
                        if (response.success) {
                            // تحديث حالة الزر
                            if (response.is_active) {
                                btn.removeClass('btn-outline-danger').addClass('btn-outline-success active');
                                btn.html('<i class="fas fa-toggle-on me-1"></i><span>نشط</span>');
                                btn.data('status', 'active');
                                btn.attr('title', 'تعطيل');

                                // تحديث شارة الحالة في الجدول
                                btn.closest('tr').find('td:nth-child(3) .badge')
                                   .removeClass('bg-danger').addClass('bg-success')
                                   .text('نشط');
                            } else {
                                btn.removeClass('btn-outline-success active').addClass('btn-outline-danger');
                                btn.html('<i class="fas fa-toggle-off me-1"></i><span>معطل</span>');
                                btn.data('status', 'inactive');
                                btn.attr('title', 'تفعيل');

                                // تحديث شارة الحالة في الجدول
                                btn.closest('tr').find('td:nth-child(3) .badge')
                                   .removeClass('bg-success').addClass('bg-danger')
                                   .text('معطل');
                            }

                            // إظهار رسالة نجاح
                            showAlert(response.message, 'success');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = 'حدث خطأ أثناء تنفيذ العملية';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        showAlert(errorMsg, 'danger');
                        // إعادة الزر إلى حالته السابقة
                        updateButtonState(btn, currentStatus === 'active');
                    },
                    complete: function() {
                        // إلغاء تأثير التحميل
                        btn.prop('disabled', false);
                    }
                });
            });

            // دالة لتحديث حالة الزر
            function updateButtonState(btn, isActive) {
                if (isActive) {
                    btn.removeClass('btn-outline-danger').addClass('btn-outline-success active');
                    btn.html('<i class="fas fa-toggle-on me-1"></i><span>نشط</span>');
                    btn.data('status', 'active');
                    btn.attr('title', 'تعطيل');
                } else {
                    btn.removeClass('btn-outline-success active').addClass('btn-outline-danger');
                    btn.html('<i class="fas fa-toggle-off me-1"></i><span>معطل</span>');
                    btn.data('status', 'inactive');
                    btn.attr('title', 'تفعيل');
                }
            }

            // دالة لإظهار رسالة تنبيه
            function showAlert(message, type) {
                const alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;

                // إضافة التنبيه إلى أعلى الصفحة
                const alertContainer = $('<div class="row"><div class="col-md-12"></div></div>');
                alertContainer.find('.col-md-12').append(alertHtml);

                // إضافة التنبيه إلى بداية المحتوى
                $('main.container').prepend(alertContainer);

                // إخفاء التنبيه بعد 3 ثواني
                setTimeout(function() {
                    alertContainer.alert('close');
                }, 3000);
            }

            // دالة لتحويل الأرقام العربية إلى إنجليزية
            function convertArabicToEnglishNumbers(str) {
                const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

                for (let i = 0; i < arabicNumbers.length; i++) {
                    str = str.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
                }
                return str;
            }

            // تطبيق تحويل الأرقام على جميع حقول الأرقام
            function setupNumberFieldsConversion() {
                // العثور على جميع حقول الأرقام
                const numberFields = document.querySelectorAll('input[type="number"], input[type="tel"], .english-number, .numeric-field');

                numberFields.forEach(function(field) {
                    // تحويل القيمة الحالية
                    if (field.value) {
                        field.value = convertArabicToEnglishNumbers(field.value);
                    }

                    // إضافة مستمع للأحداث
                    field.addEventListener('input', function() {
                        const cursorPosition = this.selectionStart;
                        const originalValue = this.value;
                        const convertedValue = convertArabicToEnglishNumbers(originalValue);

                        if (originalValue !== convertedValue) {
                            this.value = convertedValue;
                            // الحفاظ على موضع المؤشر
                            this.setSelectionRange(cursorPosition, cursorPosition);
                        }
                    });

                    // تحويل عند فقدان التركيز
                    field.addEventListener('blur', function() {
                        this.value = convertArabicToEnglishNumbers(this.value);
                    });

                    // تحويل عند اللصق
                    field.addEventListener('paste', function(e) {
                        setTimeout(() => {
                            this.value = convertArabicToEnglishNumbers(this.value);
                        }, 10);
                    });
                });
            }

            // تطبيق التحويل عند تحميل الصفحة
            setupNumberFieldsConversion();

            // تطبيق التحويل على العناصر المضافة ديناميكياً
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        setupNumberFieldsConversion();
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    </script>
</body>
</html>
